#!/usr/bin/env python3
"""
summarize_court_with_personnel.py

Enhanced court proceedings summarization that specifically preserves
and highlights personnel names and roles.

Usage:
  python summarize_court_with_personnel.py --input court_proceedings.txt --output court_summary.txt
"""

import argparse, logging, sys, re

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Court summarizer with personnel preservation")
parser.add_argument("-i","--input",  required=True, help="Raw transcript (.txt)")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-output", type=int, default=400, help="Max summary tokens")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Import dependencies
# -----------------------------------------------------------------------------
try:
    import numpy as np
    from transformers import AutoTokenizer, BartForConditionalGeneration
    import torch
    from sklearn.feature_extraction.text import TfidfVectorizer
except ImportError as e:
    logger.error("Missing packages: %s", e)
    logger.error("Install: pip install transformers torch scikit-learn numpy")
    sys.exit(1)

# -----------------------------------------------------------------------------
# 4) Personnel extraction
# -----------------------------------------------------------------------------
def extract_comprehensive_personnel(text: str) -> dict:
    """Extract all personnel information with improved regex patterns"""
    
    personnel = {
        'case_info': '',
        'judge': '',
        'prosecutor': '',
        'defense_attorney': '',
        'defendant': '',
        'victim': '',
        'witnesses': [],
        'experts': []
    }
    
    # Case information
    case_patterns = [
        r'(State v\. \w+|People v\. \w+|.*? v\. \w+)',
        r'Case No\. ([\w\-]+)',
        r'matter of ([^,]+)'
    ]
    
    for pattern in case_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            personnel['case_info'] += match.group(0) + ' '
    
    # Prosecutor - multiple patterns
    prosecutor_patterns = [
        r'PROSECUTOR.*?([A-Z][a-z]+\s+[A-Z][a-z]+)',
        r'Ms\.\s+([A-Z][a-z]+).*?on behalf of the State',
        r'Mr\.\s+([A-Z][a-z]+).*?on behalf of the State'
    ]
    
    for pattern in prosecutor_patterns:
        match = re.search(pattern, text)
        if match:
            personnel['prosecutor'] = match.group(1)
            break
    
    # Defense attorney
    defense_patterns = [
        r'DEFENSE.*?([A-Z][a-z]+\s+[A-Z][a-z]+)',
        r'([A-Z][a-z]+\s+[A-Z][a-z]+)\s+for the defendant'
    ]
    
    for pattern in defense_patterns:
        match = re.search(pattern, text)
        if match:
            personnel['defense_attorney'] = match.group(1)
            break
    
    # Defendant
    defendant_patterns = [
        r'defendant,?\s+(Mr\.\s+\w+)',
        r'defendant,?\s+([A-Z][a-z]+)',
        r'calls\s+(Mr\.\s+\w+)\s*\.\s*\(.*sworn\)'
    ]
    
    for pattern in defendant_patterns:
        match = re.search(pattern, text)
        if match:
            personnel['defendant'] = match.group(1)
            break
    
    # Victim
    victim_patterns = [
        r'victim,?\s+(Ms\.\s+\w+)',
        r'victim,?\s+([A-Z][a-z]+)',
        r'found\s+(Ms\.\s+\w+).*?lying'
    ]
    
    for pattern in victim_patterns:
        match = re.search(pattern, text)
        if match:
            personnel['victim'] = match.group(1)
            break
    
    # Witnesses and experts (people who were sworn in)
    sworn_pattern = r'\(([A-Z][A-Z\s\.]+),?\s+sworn\.\)'
    sworn_matches = re.findall(sworn_pattern, text)
    
    for match in sworn_matches:
        name = match.strip().title()
        # Categorize as expert or witness
        if any(title in match.upper() for title in ['DR.', 'DOCTOR', 'DETECTIVE']):
            personnel['experts'].append(name)
        else:
            personnel['witnesses'].append(name)
    
    return personnel

def create_personnel_summary(personnel: dict) -> str:
    """Create a formatted personnel summary"""
    summary_parts = []
    
    if personnel['case_info'].strip():
        summary_parts.append(f"Case: {personnel['case_info'].strip()}")
    
    if personnel['prosecutor']:
        summary_parts.append(f"Prosecutor: {personnel['prosecutor']}")
    
    if personnel['defense_attorney']:
        summary_parts.append(f"Defense: {personnel['defense_attorney']}")
    
    if personnel['defendant']:
        summary_parts.append(f"Defendant: {personnel['defendant']}")
    
    if personnel['victim']:
        summary_parts.append(f"Victim: {personnel['victim']}")
    
    if personnel['witnesses']:
        summary_parts.append(f"Witnesses: {', '.join(personnel['witnesses'])}")
    
    if personnel['experts']:
        summary_parts.append(f"Expert Witnesses: {', '.join(personnel['experts'])}")
    
    return ". ".join(summary_parts) + "."

# -----------------------------------------------------------------------------
# 5) Enhanced summarization
# -----------------------------------------------------------------------------
def summarize_with_personnel_focus(text: str):
    """Summarize while preserving personnel information"""
    
    logger.info("Extracting personnel information...")
    personnel = extract_comprehensive_personnel(text)
    personnel_summary = create_personnel_summary(personnel)
    
    logger.info("Personnel extracted:")
    logger.info(personnel_summary)
    
    logger.info("Loading BART model...")
    tokenizer = AutoTokenizer.from_pretrained("facebook/bart-large-cnn")
    model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")
    
    # Create focused input that emphasizes personnel and key facts
    key_sentences = extract_key_legal_sentences(text, personnel)
    
    # Create a structured prompt
    structured_input = f"{personnel_summary} Key proceedings: {key_sentences}"
    
    logger.info(f"Input length: {len(structured_input)} characters")
    
    # Tokenize and generate
    inputs = tokenizer(
        structured_input,
        return_tensors="pt",
        max_length=1024,
        truncation=True,
        padding=True
    )
    
    logger.info("Generating summary...")
    with torch.no_grad():
        summary_ids = model.generate(
            inputs.input_ids,
            attention_mask=inputs.attention_mask,
            max_length=args.max_output,
            min_length=100,
            num_beams=6,
            length_penalty=2.0,
            early_stopping=True,
            no_repeat_ngram_size=3,
            repetition_penalty=1.1
        )
    
    summary = tokenizer.decode(summary_ids[0], skip_special_tokens=True)
    
    # Ensure personnel information is preserved in final summary
    final_summary = f"{personnel_summary} {summary}".strip()
    
    return final_summary

def extract_key_legal_sentences(text: str, personnel: dict, num_sentences: int = 12) -> str:
    """Extract key sentences with focus on legal facts and evidence"""
    
    sentences = [s.strip() for s in text.replace('\n', ' ').split('.') if len(s.strip()) > 15]
    
    if len(sentences) <= num_sentences:
        return ' '.join(sentences)
    
    # Keywords that indicate important legal content
    legal_keywords = [
        'evidence', 'testimony', 'witness', 'found', 'observed', 'autopsy', 
        'gunshot', 'wound', 'forensic', 'ballistic', 'DNA', 'fingerprint',
        'motive', 'alibi', 'confession', 'admission', 'denied', 'claimed',
        'cell tower', 'phone', 'location', 'time', 'date'
    ]
    
    # Personnel names to boost
    personnel_names = [v for v in personnel.values() if isinstance(v, str) and v]
    personnel_names.extend([name for names in personnel.values() if isinstance(names, list) for name in names])
    
    try:
        # Use TF-IDF with custom vocabulary
        vectorizer = TfidfVectorizer(
            stop_words='english', 
            max_features=1000,
            vocabulary=legal_keywords + personnel_names if legal_keywords + personnel_names else None
        )
        
        tfidf_matrix = vectorizer.fit_transform(sentences)
        sentence_scores = np.array(tfidf_matrix.sum(axis=1)).flatten()
        
        # Boost sentences with legal keywords or personnel names
        for i, sentence in enumerate(sentences):
            boost_factor = 1.0
            
            # Boost for legal keywords
            for keyword in legal_keywords:
                if keyword.lower() in sentence.lower():
                    boost_factor += 0.3
            
            # Boost for personnel names
            for name in personnel_names:
                if name and name.lower() in sentence.lower():
                    boost_factor += 0.5
            
            sentence_scores[i] *= boost_factor
        
        # Get top sentences
        top_indices = sentence_scores.argsort()[-num_sentences:][::-1]
        top_indices.sort()  # Maintain original order
        
        key_sentences = [sentences[i] for i in top_indices]
        return '. '.join(key_sentences)
        
    except Exception as e:
        logger.warning(f"TF-IDF failed: {e}, using fallback")
        return '. '.join(sentences[:num_sentences])

# -----------------------------------------------------------------------------
# 6) Main function
# -----------------------------------------------------------------------------
def main():
    try:
        with open(args.input, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        logger.error(f"Could not read input file: {e}")
        sys.exit(1)
    
    logger.info(f"Input text length: {len(text)} characters")
    
    summary = summarize_with_personnel_focus(text)
    
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error(f"Could not write output file: {e}")
        sys.exit(1)
    
    logger.info(f"Summary written to {args.output}")
    logger.info(f"Summary length: {len(summary)} characters")
    
    print("\n" + "="*70)
    print("LEGAL SUMMARY WITH PERSONNEL:")
    print("="*70)
    print(summary)
    print("="*70)

if __name__ == "__main__":
    main()
