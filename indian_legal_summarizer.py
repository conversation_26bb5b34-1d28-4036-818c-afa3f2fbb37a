#!/usr/bin/env python3
"""
indian_legal_summarizer.py

INDIAN LEGAL SUMMARIZATION SYSTEM
Specifically designed for Indian courtroom proceedings with:
- Indian case citation formats (Criminal Appeal No., Crl. A. No., etc.)
- Indian legal terminology (APP, PW, IPC, CrPC, etc.)
- Indian judicial hierarchy (District Court, High Court, Supreme Court)
- Indian court personnel titles and roles
- Indian legal procedure terminology

Usage:
  python indian_legal_summarizer.py --input proceedings.txt --output summary.txt
"""

import argparse, logging, sys, re
from transformers import AutoTokenizer, BartForConditionalGeneration
from typing import Dict, List, Tuple

# -----------------------------------------------------------------------------
# Arguments
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Indian Legal Summarizer")
parser.add_argument("-i","--input", required=True, help="Input transcript file")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-narrative", type=int, default=400, help="Max narrative summary length")
args = parser.parse_args()

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# Indian Case Type Detection
# -----------------------------------------------------------------------------
def detect_indian_case_type(text: str) -> Tuple[str, float]:
    """Detect Indian case type with confidence score"""
    text_lower = text.lower()
    
    indian_patterns = {
        'criminal': {
            'keywords': ['criminal appeal', 'sessions case', 'fir', 'accused', 'ipc', 'crpc', 'police station', 
                        'investigation', 'chargesheet', 'cognizable', 'non-cognizable', 'bail', 'remand'],
            'sections': ['section 302', 'section 307', 'section 420', 'section 498a', 'section 376', 'section 363'],
            'courts': ['sessions judge', 'additional sessions judge', 'chief judicial magistrate', 'acjm']
        },
        'civil': {
            'keywords': ['civil suit', 'civil appeal', 'plaintiff', 'defendant', 'damages', 'injunction', 
                        'specific performance', 'declaration', 'possession', 'title suit'],
            'sections': ['order 7', 'order 8', 'order 39', 'cpc', 'civil procedure code'],
            'courts': ['civil judge', 'district judge', 'civil court']
        },
        'family': {
            'keywords': ['matrimonial', 'divorce', 'maintenance', 'custody', 'hindu marriage act', 
                        'muslim personal law', 'domestic violence', 'dowry'],
            'sections': ['section 13', 'section 125 crpc', 'protection of women act'],
            'courts': ['family court', 'matrimonial court']
        },
        'constitutional': {
            'keywords': ['writ petition', 'mandamus', 'certiorari', 'habeas corpus', 'fundamental rights',
                        'article 226', 'article 32', 'public interest litigation', 'pil'],
            'sections': ['article 14', 'article 19', 'article 21'],
            'courts': ['high court', 'supreme court']
        }
    }
    
    scores = {}
    for case_type, patterns in indian_patterns.items():
        score = 0
        
        # Check keywords
        for keyword in patterns['keywords']:
            score += text_lower.count(keyword) * 2
        
        # Check legal sections
        for section in patterns['sections']:
            score += text_lower.count(section) * 3
        
        # Check court types
        for court in patterns['courts']:
            score += text_lower.count(court) * 4
        
        scores[case_type] = score
    
    best_type = max(scores, key=scores.get)
    confidence = min(scores[best_type] / 15, 1.0)  # Normalize
    
    return best_type, confidence

# -----------------------------------------------------------------------------
# Indian Information Extractor
# -----------------------------------------------------------------------------
class IndianLegalExtractor:
    """Extract information specific to Indian legal proceedings"""
    
    def __init__(self, case_type: str):
        self.case_type = case_type
    
    def extract_indian_case_info(self, text: str) -> Dict:
        """Extract comprehensive Indian case information"""
        
        info = {
            # Case identification
            'case_name': self._extract_indian_case_name(text),
            'case_number': self._extract_indian_case_number(text),
            'case_type': self.case_type,
            'court_name': self._extract_court_name(text),
            'judge_name': self._extract_judge_name(text),
            'hearing_date': self._extract_hearing_date(text),
            
            # Indian legal personnel
            'public_prosecutor': self._extract_public_prosecutor(text),
            'defense_advocates': self._extract_defense_advocates(text),
            'accused_persons': self._extract_accused_persons(text),
            'complainant': self._extract_complainant(text),
            'victim': self._extract_victim(text),
            
            # Indian legal specifics
            'fir_details': self._extract_fir_details(text),
            'police_station': self._extract_police_station(text),
            'ipc_sections': self._extract_ipc_sections(text),
            'crpc_sections': self._extract_crpc_sections(text),
            'evidence_act_sections': self._extract_evidence_act_sections(text),
            
            # Witnesses and evidence
            'prosecution_witnesses': self._extract_prosecution_witnesses(text),
            'defense_witnesses': self._extract_defense_witnesses(text),
            'case_law_cited': self._extract_case_law_cited(text),
            
            # Proceedings
            'legal_arguments': self._extract_indian_legal_arguments(text),
            'court_observations': self._extract_court_observations(text),
            'next_date': self._extract_next_date(text),
            'judgment_status': self._extract_judgment_status(text)
        }
        
        return info
    
    def _extract_indian_case_name(self, text: str) -> str:
        """Extract Indian case name patterns"""
        patterns = [
            r'State \(NCT of Delhi\) v\. ([^\\n]+)',
            r'State of ([A-Za-z\s]+) v\. ([^\\n]+)',
            r'([A-Za-z\s]+) v\. State of ([A-Za-z\s]+)',
            r'([A-Za-z\s]+) v\. ([A-Za-z\s]+) & Anr',
            r'([A-Za-z\s]+) v\. ([A-Za-z\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return "Case name not identified"
    
    def _extract_indian_case_number(self, text: str) -> str:
        """Extract Indian case number formats"""
        patterns = [
            r'Criminal Appeal No\. (\d+/\d+)',
            r'Crl\. A\. No\. (\d+/\d+)',
            r'Sessions Case No\. (\d+/\d+)',
            r'Civil Appeal No\. (\d+/\d+)',
            r'Writ Petition No\. (\d+/\d+)',
            r'Case No\. (\d+/\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return "Case number not identified"
    
    def _extract_court_name(self, text: str) -> str:
        """Extract Indian court names"""
        patterns = [
            r'IN THE COURT OF ([^\\n]+)',
            r'DELHI DISTRICT COURT, ([^\\n]+)',
            r'HIGH COURT OF ([^\\n]+)',
            r'SUPREME COURT OF INDIA'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return "Court not identified"
    
    def _extract_judge_name(self, text: str) -> str:
        """Extract Indian judge names and titles"""
        patterns = [
            r'Hon\'ble Shri Justice ([A-Za-z\s\.]+)',
            r'Hon\'ble Mr\. Justice ([A-Za-z\s\.]+)',
            r'Hon\'ble Ms\. Justice ([A-Za-z\s\.]+)',
            r'Coram: Hon\'ble ([^\\n]+)',
            r'Additional Sessions Judge ([A-Za-z\s\.]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return "Judge not identified"
    
    def _extract_hearing_date(self, text: str) -> str:
        """Extract hearing dates"""
        patterns = [
            r'Date of Hearing: ([^\\n]+)',
            r'Hearing Date: ([^\\n]+)',
            r'Date: (\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return "Date not specified"
    
    def _extract_public_prosecutor(self, text: str) -> str:
        """Extract Public Prosecutor details"""
        patterns = [
            r'Shri ([A-Za-z\s]+), Additional Public Prosecutor',
            r'Ms\. ([A-Za-z\s]+), Additional Public Prosecutor',
            r'ADDITIONAL PUBLIC PROSECUTOR \(Shri ([A-Za-z\s]+)\)',
            r'APP.*?Shri ([A-Za-z\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return f"Shri {match.group(1).strip()}, Additional Public Prosecutor"
        
        return "Public Prosecutor not identified"
    
    def _extract_defense_advocates(self, text: str) -> List[str]:
        """Extract defense advocate details"""
        patterns = [
            r'Shri Advocate ([A-Za-z\s]+) for Accused No\. (\d+)',
            r'Ms\. Advocate ([A-Za-z\s]+) for Accused No\. (\d+)',
            r'ADVOCATE ([A-Za-z\s]+): Much obliged'
        ]
        
        advocates = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    advocates.append(f"Advocate {match[0]} for Accused No. {match[1]}")
                else:
                    advocates.append(f"Advocate {match}")
        
        return advocates if advocates else ["Defense advocates not identified"]
    
    def _extract_accused_persons(self, text: str) -> List[str]:
        """Extract accused persons"""
        patterns = [
            r'Accused No\. \d+ \(([A-Za-z\s]+)\)',
            r'accused ([A-Za-z\s]+) disclosed',
            r'my client ([A-Za-z\s]+) was'
        ]
        
        accused = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            accused.extend(matches)
        
        return list(set(accused)) if accused else ["Accused not identified"]
    
    def _extract_complainant(self, text: str) -> str:
        """Extract complainant details"""
        patterns = [
            r'complainant Shri ([A-Za-z\s]+)',
            r'Complainant: Shri ([A-Za-z\s]+)',
            r'complaint from ([A-Za-z\s]+), father'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return f"Shri {match.group(1).strip()}"
        
        return "Complainant not identified"
    
    def _extract_victim(self, text: str) -> str:
        """Extract victim details"""
        patterns = [
            r'victim, Smt\. ([A-Za-z\s]+)',
            r'Victim: Smt\. ([A-Za-z\s]+)',
            r'the victim ([A-Za-z\s]+) was'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return f"Smt. {match.group(1).strip()}"
        
        return "Victim not identified"
    
    def _extract_fir_details(self, text: str) -> Dict:
        """Extract FIR details"""
        fir_info = {}
        
        # FIR Number
        fir_match = re.search(r'FIR No\. (\d+/\d+)', text)
        if fir_match:
            fir_info['fir_number'] = fir_match.group(1)
        
        # FIR registration
        reg_match = re.search(r'FIR was registered under Sections ([^.]+)', text)
        if reg_match:
            fir_info['sections'] = reg_match.group(1)
        
        return fir_info
    
    def _extract_police_station(self, text: str) -> str:
        """Extract police station"""
        patterns = [
            r'PS ([A-Za-z\s]+) has deposed',
            r'Police Station: ([A-Za-z\s]+)',
            r'of PS ([A-Za-z\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return "Police Station not identified"
    
    def _extract_ipc_sections(self, text: str) -> List[str]:
        """Extract IPC sections"""
        patterns = [
            r'Section (\d+[A-Za-z]*) (?:of the )?(?:Indian Penal Code|IPC)',
            r'Sections ([0-9, ]+) IPC',
            r'under Section (\d+[A-Za-z]*)'
        ]
        
        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            sections.extend(matches)
        
        return list(set(sections)) if sections else ["IPC sections not specified"]
    
    def _extract_crpc_sections(self, text: str) -> List[str]:
        """Extract CrPC sections"""
        patterns = [
            r'Section (\d+[A-Za-z]*) CrPC',
            r'under Section (\d+[A-Za-z]*) of.*?Criminal Procedure Code'
        ]
        
        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            sections.extend(matches)
        
        return list(set(sections)) if sections else ["CrPC sections not specified"]
    
    def _extract_evidence_act_sections(self, text: str) -> List[str]:
        """Extract Evidence Act sections"""
        patterns = [
            r'Section (\d+[A-Za-z]*) of the Evidence Act',
            r'under Section (\d+[A-Za-z]*) Evidence Act'
        ]
        
        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            sections.extend(matches)
        
        return list(set(sections)) if sections else ["Evidence Act sections not specified"]
    
    def _extract_prosecution_witnesses(self, text: str) -> List[str]:
        """Extract prosecution witnesses (PW)"""
        patterns = [
            r'PW-(\d+) ([A-Za-z\s]+) (?:of PS|from|has deposed)',
            r'PW-(\d+) ([A-Za-z\s\.]+) examined'
        ]
        
        witnesses = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                witnesses.append(f"PW-{match[0]} {match[1].strip()}")
        
        return witnesses if witnesses else ["Prosecution witnesses not identified"]
    
    def _extract_defense_witnesses(self, text: str) -> List[str]:
        """Extract defense witnesses (DW)"""
        patterns = [
            r'DW-(\d+) ([A-Za-z\s]+)',
            r'defense witness ([A-Za-z\s]+)'
        ]
        
        witnesses = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            witnesses.extend([f"DW-{m[0]} {m[1]}" if isinstance(m, tuple) else f"Defense witness {m}" for m in matches])
        
        return witnesses if witnesses else ["Defense witnesses not identified"]
    
    def _extract_case_law_cited(self, text: str) -> List[str]:
        """Extract cited case law"""
        patterns = [
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+), \((\d{4})\) (\d+) SCC (\d+)',
            r'in ([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+)'
        ]
        
        cases = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) == 5:  # Full citation
                    cases.append(f"{match[0]} v. {match[1]}, ({match[2]}) {match[3]} SCC {match[4]}")
                else:  # Simple citation
                    cases.append(f"{match[0]} v. {match[1]}")
        
        return list(set(cases)) if cases else ["No case law cited"]
    
    def _extract_indian_legal_arguments(self, text: str) -> List[str]:
        """Extract legal arguments specific to Indian law"""
        arguments = []
        
        # Look for advocate arguments
        advocate_patterns = [
            r'ADVOCATE [A-Za-z\s]+: ([^\\n]+)',
            r'learned counsel.*?: ([^\\n]+)',
            r'My Lord, ([^.]+\\.)'
        ]
        
        for pattern in advocate_patterns:
            matches = re.findall(pattern, text)
            arguments.extend(matches)
        
        return arguments[:5] if arguments else ["Legal arguments not identified"]
    
    def _extract_court_observations(self, text: str) -> List[str]:
        """Extract court observations and questions"""
        observations = []
        
        court_patterns = [
            r'COURT: ([^\\n]+)',
            r'My Lord.*?([^.]+\\.)'
        ]
        
        for pattern in court_patterns:
            matches = re.findall(pattern, text)
            observations.extend(matches)
        
        return observations[:5] if observations else ["Court observations not recorded"]
    
    def _extract_next_date(self, text: str) -> str:
        """Extract next hearing date"""
        patterns = [
            r'adjourned to ([^\\n]+) for',
            r'next date.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})',
            r'case is adjourned to ([^\\n]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return "Next date not specified"
    
    def _extract_judgment_status(self, text: str) -> str:
        """Extract judgment status"""
        if 'judgment is reserved' in text.lower():
            return "Judgment Reserved"
        elif 'judgment pronounced' in text.lower():
            return "Judgment Pronounced"
        elif 'arguments concluded' in text.lower():
            return "Arguments Concluded"
        else:
            return "Proceedings ongoing"

# -----------------------------------------------------------------------------
# Indian Summary Generator
# -----------------------------------------------------------------------------
class IndianLegalSummaryGenerator:
    """Generate summaries adapted for Indian legal documentation"""

    def __init__(self, case_type: str):
        self.case_type = case_type

    def generate_indian_hybrid_summary(self, case_info: Dict, original_text: str) -> str:
        """Generate Indian legal hybrid summary"""

        # Generate Indian narrative summary
        narrative_summary = self._generate_indian_narrative(case_info, original_text)

        # Generate Indian structured analysis
        structured_analysis = self._generate_indian_structured_analysis(case_info)

        # Combine in Indian legal format
        hybrid_summary = f"""{'='*100}
INDIAN LEGAL CASE ANALYSIS - HYBRID FORMAT
{'='*100}

EXECUTIVE SUMMARY (कार्यकारी सारांश):
{'-'*60}
{narrative_summary}

{'='*100}
DETAILED STRUCTURED ANALYSIS (विस्तृत संरचित विश्लेषण)
{'='*100}
{structured_analysis}

{'='*100}
END OF ANALYSIS (विश्लेषण समाप्त)
{'='*100}"""

        return hybrid_summary

    def _generate_indian_narrative(self, case_info: Dict, original_text: str) -> str:
        """Generate narrative summary for Indian legal case"""

        narrative_parts = []

        # Case introduction with Indian format
        intro = self._create_indian_case_introduction(case_info)
        if intro:
            narrative_parts.append(intro)

        # Court and personnel
        court_info = self._create_indian_court_info(case_info)
        if court_info:
            narrative_parts.append(court_info)

        # Legal proceedings
        proceedings = self._create_indian_proceedings_narrative(case_info, original_text)
        if proceedings:
            narrative_parts.append(proceedings)

        # Arguments and outcome
        outcome = self._create_indian_outcome_narrative(case_info)
        if outcome:
            narrative_parts.append(outcome)

        return " ".join(narrative_parts)

    def _create_indian_case_introduction(self, case_info: Dict) -> str:
        """Create Indian case introduction"""

        intro_parts = []

        # Case identification
        case_name = case_info.get('case_name', '')
        case_number = case_info.get('case_number', '')

        if case_name and 'not identified' not in case_name:
            intro = f"In the matter of {case_name}"
            if case_number and 'not identified' not in case_number:
                intro += f" ({case_number})"
            intro_parts.append(intro)

        # Court details
        court_name = case_info.get('court_name', '')
        if court_name and 'not identified' not in court_name:
            intro_parts.append(f"before the {court_name}")

        # Judge details
        judge_name = case_info.get('judge_name', '')
        if judge_name and 'not identified' not in judge_name:
            intro_parts.append(f"presided over by Hon'ble {judge_name}")

        return ", ".join(intro_parts) + "." if intro_parts else ""

    def _create_indian_court_info(self, case_info: Dict) -> str:
        """Create court personnel information"""

        court_parts = []

        # Public Prosecutor
        pp = case_info.get('public_prosecutor', '')
        if pp and 'not identified' not in pp:
            court_parts.append(f"The case was argued by {pp} for the State")

        # Defense advocates
        defense_advocates = case_info.get('defense_advocates', [])
        if defense_advocates and 'not identified' not in str(defense_advocates):
            advocates_str = ', '.join(defense_advocates)
            court_parts.append(f"while the accused were represented by {advocates_str}")

        return ". ".join(court_parts) + "." if court_parts else ""

    def _create_indian_proceedings_narrative(self, case_info: Dict, original_text: str) -> str:
        """Create proceedings narrative for Indian case"""

        proceedings_parts = []

        if self.case_type == 'criminal':
            proceedings_parts.extend(self._create_criminal_proceedings_narrative(case_info, original_text))
        elif self.case_type == 'civil':
            proceedings_parts.extend(self._create_civil_proceedings_narrative(case_info, original_text))
        elif self.case_type == 'constitutional':
            proceedings_parts.extend(self._create_constitutional_proceedings_narrative(case_info, original_text))

        return ". ".join(proceedings_parts) + "." if proceedings_parts else ""

    def _create_criminal_proceedings_narrative(self, case_info: Dict, original_text: str) -> List[str]:
        """Create criminal case proceedings narrative"""

        parts = []

        # FIR and charges
        fir_details = case_info.get('fir_details', {})
        ipc_sections = case_info.get('ipc_sections', [])

        if fir_details.get('fir_number'):
            parts.append(f"An FIR No. {fir_details['fir_number']} was registered")

        if ipc_sections and 'not specified' not in str(ipc_sections):
            sections_str = ', '.join(ipc_sections)
            parts.append(f"under Sections {sections_str} of the Indian Penal Code")

        # Investigation and witnesses
        police_station = case_info.get('police_station', '')
        if police_station and 'not identified' not in police_station:
            parts.append(f"Investigation was conducted by {police_station} Police Station")

        # Prosecution witnesses
        pw_witnesses = case_info.get('prosecution_witnesses', [])
        if pw_witnesses and 'not identified' not in str(pw_witnesses):
            parts.append(f"The prosecution examined witnesses including {', '.join(pw_witnesses[:3])}")

        # Key evidence from text
        if 'recovery' in original_text.lower():
            parts.append("Recovery of evidence was made during investigation")

        if 'disclosure statement' in original_text.lower():
            parts.append("Disclosure statements were recorded under Section 27 of the Evidence Act")

        return parts

    def _create_civil_proceedings_narrative(self, case_info: Dict, original_text: str) -> List[str]:
        """Create civil case proceedings narrative"""

        parts = []

        if 'plaintiff' in original_text.lower():
            parts.append("The plaintiff filed a suit seeking relief")

        if 'damages' in original_text.lower():
            parts.append("claiming damages for the alleged wrongs")

        if 'injunction' in original_text.lower():
            parts.append("and seeking injunctive relief")

        return parts

    def _create_constitutional_proceedings_narrative(self, case_info: Dict, original_text: str) -> List[str]:
        """Create constitutional case proceedings narrative"""

        parts = []

        if 'writ petition' in original_text.lower():
            parts.append("A writ petition was filed")

        if 'fundamental rights' in original_text.lower():
            parts.append("alleging violation of fundamental rights")

        if 'article' in original_text.lower():
            parts.append("under various Articles of the Constitution")

        return parts

    def _create_indian_outcome_narrative(self, case_info: Dict) -> str:
        """Create outcome narrative"""

        outcome_parts = []

        # Judgment status
        judgment_status = case_info.get('judgment_status', '')
        if judgment_status and 'ongoing' not in judgment_status:
            outcome_parts.append(f"The {judgment_status.lower()}")

        # Next date
        next_date = case_info.get('next_date', '')
        if next_date and 'not specified' not in next_date:
            outcome_parts.append(f"with the matter adjourned to {next_date}")

        # Case law cited
        case_law = case_info.get('case_law_cited', [])
        if case_law and 'No case law' not in str(case_law):
            outcome_parts.append(f"The court referred to precedents including {case_law[0]}")

        return ". ".join(outcome_parts) + "." if outcome_parts else "The matter remains pending before the court."

    def _generate_indian_structured_analysis(self, case_info: Dict) -> str:
        """Generate Indian structured analysis"""

        structure = f"""CASE IDENTIFICATION (मामले की पहचान):
Case Name: {case_info.get('case_name', 'Not identified')}
Case Number: {case_info.get('case_number', 'Not identified')}
Case Type: {case_info.get('case_type', 'Not specified').title()}
Court: {case_info.get('court_name', 'Not identified')}
Judge: Hon'ble {case_info.get('judge_name', 'Not identified')}
Hearing Date: {case_info.get('hearing_date', 'Not specified')}

LEGAL PERSONNEL (कानूनी कर्मचारी):
Public Prosecutor: {case_info.get('public_prosecutor', 'Not identified')}
Defense Advocates: {self._format_list(case_info.get('defense_advocates', ['Not identified']))}

PARTIES (पक्षकार):
Accused Persons: {self._format_list(case_info.get('accused_persons', ['Not identified']))}
Complainant: {case_info.get('complainant', 'Not identified')}
Victim: {case_info.get('victim', 'Not identified')}

FIR DETAILS (एफआईआर विवरण):
FIR Information: {self._format_dict(case_info.get('fir_details', {}))}
Police Station: {case_info.get('police_station', 'Not identified')}

LEGAL PROVISIONS (कानूनी प्रावधान):
IPC Sections: {self._format_list(case_info.get('ipc_sections', ['Not specified']))}
CrPC Sections: {self._format_list(case_info.get('crpc_sections', ['Not specified']))}
Evidence Act Sections: {self._format_list(case_info.get('evidence_act_sections', ['Not specified']))}

WITNESSES (गवाह):
Prosecution Witnesses: {self._format_list(case_info.get('prosecution_witnesses', ['Not identified']))}
Defense Witnesses: {self._format_list(case_info.get('defense_witnesses', ['Not identified']))}

CASE LAW CITED (उद्धृत मामला कानून):
{self._format_list(case_info.get('case_law_cited', ['No case law cited']))}

LEGAL ARGUMENTS (कानूनी तर्क):
{self._format_list(case_info.get('legal_arguments', ['Not recorded']))}

COURT OBSERVATIONS (न्यायालय की टिप्पणियां):
{self._format_list(case_info.get('court_observations', ['Not recorded']))}

JUDGMENT STATUS (निर्णय की स्थिति):
Status: {case_info.get('judgment_status', 'Proceedings ongoing')}
Next Date: {case_info.get('next_date', 'Not specified')}"""

        return structure

    def _format_list(self, items: List[str]) -> str:
        """Format list items for Indian legal display"""
        if not items or (len(items) == 1 and ('not' in items[0].lower() or 'none' in items[0].lower())):
            return "• Not specified"
        return '\n'.join([f"• {item}" for item in items if item])

    def _format_dict(self, items: Dict) -> str:
        """Format dictionary items for Indian legal display"""
        if not items:
            return "• Not specified"
        return '\n'.join([f"• {k.title()}: {v}" for k, v in items.items() if v])

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------
def main():
    logger.info("Starting Indian Legal Summarizer...")

    # Read input
    try:
        with open(args.input, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        logger.error(f"Could not read input file: {e}")
        sys.exit(1)

    logger.info(f"Input text length: {len(text)} characters")

    # Detect Indian case type
    case_type, confidence = detect_indian_case_type(text)
    logger.info(f"Detected Indian case type: {case_type} (confidence: {confidence:.2f})")

    # Extract Indian case information
    extractor = IndianLegalExtractor(case_type)
    case_info = extractor.extract_indian_case_info(text)

    # Generate Indian hybrid summary
    generator = IndianLegalSummaryGenerator(case_type)
    indian_summary = generator.generate_indian_hybrid_summary(case_info, text)

    # Write output
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(indian_summary)
    except Exception as e:
        logger.error(f"Could not write output file: {e}")
        sys.exit(1)

    logger.info(f"Indian legal summary written to {args.output}")
    logger.info(f"Summary length: {len(indian_summary)} characters")

    print(indian_summary)

if __name__ == "__main__":
    main()
