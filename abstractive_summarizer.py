#!/usr/bin/env python3
"""
abstractive_summarizer.py - Pure abstractive summary for comparison
"""

import argparse, logging, sys
from transformers import AutoTokenizer, BartForConditionalGeneration
import torch

parser = argparse.ArgumentParser("Abstractive Legal Summarizer")
parser.add_argument("-i","--input", required=True, help="Input file")
parser.add_argument("-o","--output", required=True, help="Output file")
args = parser.parse_args()

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(message)s")
logger = logging.getLogger(__name__)

def create_abstractive_summary(text: str) -> str:
    """Create pure abstractive summary"""
    
    logger.info("Creating abstractive summary...")
    
    # Load BART
    tokenizer = AutoTokenizer.from_pretrained("facebook/bart-large-cnn")
    model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")
    
    # Create comprehensive prompt
    prompt = f"Provide a comprehensive narrative summary of this legal case including all key personnel, evidence, testimony, and legal arguments: {text[:1500]}"
    
    inputs = tokenizer(
        prompt,
        return_tensors="pt",
        max_length=1024,
        truncation=True
    )
    
    with torch.no_grad():
        summary_ids = model.generate(
            inputs.input_ids,
            max_length=400,
            min_length=150,
            num_beams=6,
            length_penalty=2.5,
            early_stopping=True,
            no_repeat_ngram_size=3
        )
    
    summary = tokenizer.decode(summary_ids[0], skip_special_tokens=True)
    
    # Clean up
    if "Provide a comprehensive" in summary:
        summary = summary.split(":")[-1].strip()
    
    return summary

def main():
    with open(args.input, "r", encoding="utf-8") as f:
        text = f.read()
    
    summary = create_abstractive_summary(text)
    
    with open(args.output, "w", encoding="utf-8") as f:
        f.write(summary)
    
    logger.info(f"Abstractive summary written to {args.output}")
    
    print("\n" + "="*80)
    print("ABSTRACTIVE LEGAL SUMMARY:")
    print("="*80)
    print(summary)
    print("="*80)

if __name__ == "__main__":
    main()
