#!/usr/bin/env python3
"""
summarize_court_legal_bert_bart.py

Advanced court proceedings summarization using Legal-BERT for understanding
and BART for generation - combining the best of both worlds.

Usage:
  python summarize_court_legal_bert_bart.py --input court_proceedings.txt --output court_summary.txt
"""

import argparse, logging, sys

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Court summarizer with Legal-BERT + BART")
parser.add_argument("-i","--input",  required=True, help="Raw transcript (.txt)")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-input",  type=int, default=1024, help="Max input tokens")
parser.add_argument("--max-output", type=int, default=300,   help="Max summary tokens")
parser.add_argument("--min-output", type=int, default=80,    help="Min summary tokens")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Import dependencies
# -----------------------------------------------------------------------------
try:
    import numpy as np
    from transformers import (
        AutoTokenizer, AutoModel, AutoModelForSeq2SeqLM, 
        pipeline, BartForConditionalGeneration
    )
    import torch
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
except ImportError as e:
    logger.error("Missing Python package: %s", e)
    logger.error("Please install: pip install transformers torch scikit-learn numpy")
    sys.exit(1)

device = "cpu"

# -----------------------------------------------------------------------------
# 4) Read & preprocess text
# -----------------------------------------------------------------------------
def read_and_clean(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            raw = f.read()
    except Exception as e:
        logger.error("Could not read input file: %s", e)
        sys.exit(1)
    
    lines = [ln.strip() for ln in raw.splitlines() if ln.strip()]
    return "\n".join(lines)

def extract_personnel_info(text: str) -> dict:
    """Extract key personnel information from legal text"""
    import re

    personnel = {
        'case': '',
        'judge': '',
        'prosecutor': '',
        'defense': '',
        'defendant': '',
        'victim': '',
        'witnesses': []
    }

    # Extract case information
    case_match = re.search(r'(State v\. \w+|People v\. \w+|.*v\. \w+)', text)
    if case_match:
        personnel['case'] = case_match.group(1)

    case_num_match = re.search(r'Case No\. ([\w\-]+)', text)
    if case_num_match:
        personnel['case'] += f", Case No. {case_num_match.group(1)}"

    # Extract prosecutor
    prosecutor_match = re.search(r'PROSECUTOR.*?([A-Z][a-z]+ [A-Z][a-z]+)', text)
    if prosecutor_match:
        personnel['prosecutor'] = prosecutor_match.group(1)

    # Extract defense attorney
    defense_match = re.search(r'DEFENSE.*?([A-Z][a-z]+ [A-Z][a-z]+)', text)
    if defense_match:
        personnel['defense'] = defense_match.group(1)

    # Extract defendant
    defendant_match = re.search(r'defendant,? (Mr\. \w+|Ms\. \w+|\w+ \w+)', text)
    if defendant_match:
        personnel['defendant'] = defendant_match.group(1)

    # Extract victim
    victim_match = re.search(r'victim,? (Mr\. \w+|Ms\. \w+|\w+ \w+)', text)
    if victim_match:
        personnel['victim'] = victim_match.group(1)

    # Extract witnesses (people who were sworn in)
    witness_pattern = r'\(([A-Z][A-Z\s]+),? sworn\.\)'
    witnesses = re.findall(witness_pattern, text)
    personnel['witnesses'] = [w.title() for w in witnesses]

    return personnel

def extract_key_sentences(text: str, num_sentences: int = 10) -> str:
    """Extract key sentences using TF-IDF, ensuring personnel names are preserved"""
    sentences = [s.strip() for s in text.replace('\n', ' ').split('.') if len(s.strip()) > 20]

    if len(sentences) <= num_sentences:
        return text

    # Boost sentences containing names and legal roles
    name_keywords = ['JUDGE', 'PROSECUTOR', 'DEFENSE', 'DEFENDANT', 'WITNESS', 'Dr.', 'Officer', 'Detective', 'Mr.', 'Ms.', 'sworn']

    # Use TF-IDF to find most important sentences
    vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)
    try:
        tfidf_matrix = vectorizer.fit_transform(sentences)
        sentence_scores = np.array(tfidf_matrix.sum(axis=1)).flatten()

        # Boost scores for sentences with names/roles
        for i, sentence in enumerate(sentences):
            if any(keyword in sentence for keyword in name_keywords):
                sentence_scores[i] *= 1.5  # Boost sentences with personnel info

        # Get top sentences
        top_indices = sentence_scores.argsort()[-num_sentences:][::-1]
        top_indices.sort()  # Keep original order

        key_sentences = [sentences[i] for i in top_indices]
        return '. '.join(key_sentences) + '.'
    except:
        # Fallback to first sentences if TF-IDF fails
        return '. '.join(sentences[:num_sentences]) + '.'

# -----------------------------------------------------------------------------
# 5) Legal-aware summarization
# -----------------------------------------------------------------------------
def summarize_legal_document(text: str):
    try:
        logger.info("Extracting personnel information...")

        # Extract personnel information first
        personnel = extract_personnel_info(text)

        logger.info("Loading BART for summarization...")

        # Load BART for summarization
        bart_tokenizer = AutoTokenizer.from_pretrained("facebook/bart-large-cnn")
        bart_model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")

        # Extract key sentences to fit BART's context window
        logger.info("Extracting key legal content...")
        key_content = extract_key_sentences(text, num_sentences=15)

        logger.info(f"Reduced text from {len(text)} to {len(key_content)} characters")

        # Create personnel context for better summarization
        personnel_context = ""
        if personnel['case']:
            personnel_context += f"Case: {personnel['case']}. "
        if personnel['prosecutor']:
            personnel_context += f"Prosecutor: {personnel['prosecutor']}. "
        if personnel['defense']:
            personnel_context += f"Defense Attorney: {personnel['defense']}. "
        if personnel['defendant']:
            personnel_context += f"Defendant: {personnel['defendant']}. "
        if personnel['victim']:
            personnel_context += f"Victim: {personnel['victim']}. "
        if personnel['witnesses']:
            personnel_context += f"Key Witnesses: {', '.join(personnel['witnesses'])}. "

        # Create legal-specific prompt with personnel information
        legal_prompt = f"Summarize this court case including all personnel names: {personnel_context} Court proceedings: {key_content}"
        
        # Tokenize for BART
        inputs = bart_tokenizer(
            legal_prompt,
            return_tensors="pt",
            max_length=1024,
            truncation=True,
            padding=True
        )
        
        logger.info("Generating legal summary...")
        
        # Generate summary with legal-focused parameters
        with torch.no_grad():
            summary_ids = bart_model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_length=args.max_output,
                min_length=args.min_output,
                num_beams=6,  # More beams for better quality
                length_penalty=2.5,  # Encourage longer, more detailed summaries
                early_stopping=True,
                no_repeat_ngram_size=3,
                do_sample=False,
                repetition_penalty=1.2
            )
        
        # Decode summary
        summary = bart_tokenizer.decode(summary_ids[0], skip_special_tokens=True)

        # Clean up the summary (remove any prompt remnants)
        if "Summarize this court case" in summary:
            summary = summary.split("Court proceedings:")[-1].strip()
        if "Personnel Information:" in summary:
            summary = summary.split("Personnel Information:")[-1].strip()

        # Add personnel information to the beginning of the summary if not already included
        if personnel_context and not any(name in summary for name in [personnel['defendant'], personnel['victim']] if name):
            summary = f"{personnel_context.strip()} {summary}"

        return summary.strip()
        
    except Exception as e:
        logger.error("Legal summarization failed: %s", e)
        sys.exit(1)

# -----------------------------------------------------------------------------
# 6) Main
# -----------------------------------------------------------------------------
def main():
    text = read_and_clean(args.input)
    logger.info(f"Input text length: {len(text)} characters")
    
    summary = summarize_legal_document(text)
    
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error("Could not write output file: %s", e)
        sys.exit(1)

    logger.info("Summary written to %s", args.output)
    logger.info(f"Summary length: {len(summary)} characters")
    
    print("\n" + "="*60)
    print("LEGAL DOCUMENT SUMMARY:")
    print("="*60)
    print(summary)
    print("="*60)

if __name__ == "__main__":
    main()
