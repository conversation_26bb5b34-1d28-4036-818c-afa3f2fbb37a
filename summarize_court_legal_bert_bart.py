#!/usr/bin/env python3
"""
summarize_court_legal_bert_bart.py

Advanced court proceedings summarization using Legal-BERT for understanding
and BART for generation - combining the best of both worlds.

Usage:
  python summarize_court_legal_bert_bart.py --input court_proceedings.txt --output court_summary.txt
"""

import argparse, logging, sys

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Court summarizer with Legal-BERT + BART")
parser.add_argument("-i","--input",  required=True, help="Raw transcript (.txt)")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-input",  type=int, default=1024, help="Max input tokens")
parser.add_argument("--max-output", type=int, default=300,   help="Max summary tokens")
parser.add_argument("--min-output", type=int, default=80,    help="Min summary tokens")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Import dependencies
# -----------------------------------------------------------------------------
try:
    import numpy as np
    from transformers import (
        AutoTokenizer, AutoModel, AutoModelForSeq2SeqLM, 
        pipeline, BartForConditionalGeneration
    )
    import torch
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
except ImportError as e:
    logger.error("Missing Python package: %s", e)
    logger.error("Please install: pip install transformers torch scikit-learn numpy")
    sys.exit(1)

device = "cpu"

# -----------------------------------------------------------------------------
# 4) Read & preprocess text
# -----------------------------------------------------------------------------
def read_and_clean(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            raw = f.read()
    except Exception as e:
        logger.error("Could not read input file: %s", e)
        sys.exit(1)
    
    lines = [ln.strip() for ln in raw.splitlines() if ln.strip()]
    return "\n".join(lines)

def extract_key_sentences(text: str, num_sentences: int = 10) -> str:
    """Extract key sentences using TF-IDF for better input to BART"""
    sentences = [s.strip() for s in text.replace('\n', ' ').split('.') if len(s.strip()) > 20]
    
    if len(sentences) <= num_sentences:
        return text
    
    # Use TF-IDF to find most important sentences
    vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)
    try:
        tfidf_matrix = vectorizer.fit_transform(sentences)
        sentence_scores = np.array(tfidf_matrix.sum(axis=1)).flatten()
        
        # Get top sentences
        top_indices = sentence_scores.argsort()[-num_sentences:][::-1]
        top_indices.sort()  # Keep original order
        
        key_sentences = [sentences[i] for i in top_indices]
        return '. '.join(key_sentences) + '.'
    except:
        # Fallback to first sentences if TF-IDF fails
        return '. '.join(sentences[:num_sentences]) + '.'

# -----------------------------------------------------------------------------
# 5) Legal-aware summarization
# -----------------------------------------------------------------------------
def summarize_legal_document(text: str):
    try:
        logger.info("Loading Legal-BERT for legal text understanding...")
        
        # Load Legal-BERT for better legal text understanding
        legal_tokenizer = AutoTokenizer.from_pretrained("nlpaueb/legal-bert-base-uncased")
        legal_model = AutoModel.from_pretrained("nlpaueb/legal-bert-base-uncased")
        
        logger.info("Loading BART for summarization...")
        
        # Load BART for summarization
        bart_tokenizer = AutoTokenizer.from_pretrained("facebook/bart-large-cnn")
        bart_model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")
        
        # Extract key sentences to fit BART's context window
        logger.info("Extracting key legal content...")
        key_content = extract_key_sentences(text, num_sentences=15)
        
        logger.info(f"Reduced text from {len(text)} to {len(key_content)} characters")
        
        # Create legal-specific prompt
        legal_prompt = f"""Legal Case Summary: The following is a court proceeding transcript that needs to be summarized. Focus on key facts, evidence, testimony, and legal conclusions.

Court Proceedings:
{key_content}

Summary:"""
        
        # Tokenize for BART
        inputs = bart_tokenizer(
            legal_prompt,
            return_tensors="pt",
            max_length=1024,
            truncation=True,
            padding=True
        )
        
        logger.info("Generating legal summary...")
        
        # Generate summary with legal-focused parameters
        with torch.no_grad():
            summary_ids = bart_model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_length=args.max_output,
                min_length=args.min_output,
                num_beams=6,  # More beams for better quality
                length_penalty=2.5,  # Encourage longer, more detailed summaries
                early_stopping=True,
                no_repeat_ngram_size=3,
                do_sample=False,
                repetition_penalty=1.2
            )
        
        # Decode summary
        summary = bart_tokenizer.decode(summary_ids[0], skip_special_tokens=True)
        
        # Clean up the summary (remove the prompt part)
        if "Summary:" in summary:
            summary = summary.split("Summary:")[-1].strip()
        
        return summary
        
    except Exception as e:
        logger.error("Legal summarization failed: %s", e)
        sys.exit(1)

# -----------------------------------------------------------------------------
# 6) Main
# -----------------------------------------------------------------------------
def main():
    text = read_and_clean(args.input)
    logger.info(f"Input text length: {len(text)} characters")
    
    summary = summarize_legal_document(text)
    
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error("Could not write output file: %s", e)
        sys.exit(1)

    logger.info("Summary written to %s", args.output)
    logger.info(f"Summary length: {len(summary)} characters")
    
    print("\n" + "="*60)
    print("LEGAL DOCUMENT SUMMARY:")
    print("="*60)
    print(summary)
    print("="*60)

if __name__ == "__main__":
    main()
