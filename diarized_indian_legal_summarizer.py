#!/usr/bin/env python3
"""
diarized_indian_legal_summarizer.py

DIARIZED INDIAN LEGAL SUMMARIZATION SYSTEM
Specifically designed for speaker-labeled (diarized) Indian courtroom transcripts with:
- Speaker label recognition ([JUDGE]:, [PUBLIC_PROSECUTOR]:, [ADVOCATE_NAME]:, etc.)
- Content attribution to specific speakers
- Enhanced extraction from speaker-specific content
- Proper handling of witness testimony with speaker labels

Usage:
  python diarized_indian_legal_summarizer.py --input diarized_proceedings.txt --output summary.txt
"""

import argparse, logging, sys, re
from typing import Dict, List, Tuple

# -----------------------------------------------------------------------------
# Arguments
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Diarized Indian Legal Summarizer")
parser.add_argument("-i","--input", required=True, help="Input diarized transcript file")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-narrative", type=int, default=400, help="Max narrative summary length")
args = parser.parse_args()

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# Diarized Content Parser
# -----------------------------------------------------------------------------
class DiarizedContentParser:
    """Parse speaker-labeled content from diarized transcripts"""
    
    def __init__(self):
        self.speaker_patterns = {
            'judge': [r'\[JUDGE\]:', r'\[COURT\]:', r'JUDGE:', r'COURT:'],
            'prosecutor': [r'\[PUBLIC_PROSECUTOR\]:', r'\[APP\]:', r'PUBLIC_PROSECUTOR:', r'APP:'],
            'defense': [r'\[ADVOCATE_[A-Z_]+\]:', r'ADVOCATE [A-Z\s]+:', r'\[DEFENSE\]:'],
            'witness': [r'\[[A-Z_]+\]:', r'[A-Z_]+:'],
            'clerk': [r'\[COURT_CLERK\]:', r'COURT_CLERK:', r'\[REGISTRAR\]:']
        }
    
    def parse_diarized_content(self, text: str) -> Dict:
        """Parse diarized content and organize by speakers"""
        
        parsed_content = {
            'judge_statements': [],
            'prosecutor_statements': [],
            'defense_statements': [],
            'witness_statements': [],
            'clerk_statements': [],
            'speaker_mapping': {},
            'all_speakers': []
        }
        
        # Split text into speaker segments
        speaker_segments = self._split_by_speakers(text)
        
        for speaker, content in speaker_segments:
            speaker_type = self._identify_speaker_type(speaker)
            cleaned_content = self._clean_speaker_content(content)
            
            if speaker_type == 'judge':
                parsed_content['judge_statements'].append(cleaned_content)
            elif speaker_type == 'prosecutor':
                parsed_content['prosecutor_statements'].append(cleaned_content)
            elif speaker_type == 'defense':
                parsed_content['defense_statements'].append(cleaned_content)
            elif speaker_type == 'witness':
                parsed_content['witness_statements'].append(cleaned_content)
            elif speaker_type == 'clerk':
                parsed_content['clerk_statements'].append(cleaned_content)
            
            parsed_content['speaker_mapping'][speaker] = speaker_type
            if speaker not in parsed_content['all_speakers']:
                parsed_content['all_speakers'].append(speaker)
        
        return parsed_content
    
    def _split_by_speakers(self, text: str) -> List[Tuple[str, str]]:
        """Split text by speaker labels"""
        
        # Pattern to match speaker labels
        speaker_pattern = r'\[([A-Z_]+)\]:\s*|([A-Z_\s]+):\s*'
        
        segments = []
        current_speaker = None
        current_content = []
        
        lines = text.split('\n')
        
        for line in lines:
            speaker_match = re.match(speaker_pattern, line)
            
            if speaker_match:
                # Save previous speaker's content
                if current_speaker and current_content:
                    segments.append((current_speaker, '\n'.join(current_content)))
                
                # Start new speaker
                current_speaker = speaker_match.group(1) or speaker_match.group(2)
                current_content = [line[speaker_match.end():].strip()]
            else:
                if current_speaker:
                    current_content.append(line)
        
        # Add last speaker's content
        if current_speaker and current_content:
            segments.append((current_speaker, '\n'.join(current_content)))
        
        return segments
    
    def _identify_speaker_type(self, speaker: str) -> str:
        """Identify the type of speaker"""
        
        speaker_lower = speaker.lower()
        
        if any(word in speaker_lower for word in ['judge', 'court', 'lordship']):
            return 'judge'
        elif any(word in speaker_lower for word in ['prosecutor', 'app', 'public']):
            return 'prosecutor'
        elif any(word in speaker_lower for word in ['advocate', 'defense', 'counsel']):
            return 'defense'
        elif any(word in speaker_lower for word in ['clerk', 'registrar']):
            return 'clerk'
        else:
            return 'witness'
    
    def _clean_speaker_content(self, content: str) -> str:
        """Clean speaker content"""
        
        # Remove speaker labels from content
        content = re.sub(r'\[[A-Z_]+\]:\s*', '', content)
        content = re.sub(r'^[A-Z_\s]+:\s*', '', content, flags=re.MULTILINE)
        
        # Clean up whitespace
        content = re.sub(r'\s+', ' ', content).strip()
        
        return content

# -----------------------------------------------------------------------------
# Diarized Information Extractor
# -----------------------------------------------------------------------------
class DiarizedIndianLegalExtractor:
    """Extract information from diarized Indian legal transcripts"""
    
    def __init__(self, case_type: str):
        self.case_type = case_type
        self.parser = DiarizedContentParser()
    
    def extract_diarized_case_info(self, text: str) -> Dict:
        """Extract comprehensive information from diarized transcript"""
        
        # Parse diarized content first
        parsed_content = self.parser.parse_diarized_content(text)
        
        info = {
            # Basic case information
            'case_name': self._extract_case_name_diarized(text),
            'case_number': self._extract_case_number_diarized(text),
            'case_type': self.case_type,
            'court_name': self._extract_court_name_diarized(text),
            'judge_name': self._extract_judge_name_diarized(text),
            'hearing_date': self._extract_hearing_date_diarized(text),
            
            # Personnel from speaker labels
            'public_prosecutor': self._extract_prosecutor_from_speakers(parsed_content),
            'defense_advocates': self._extract_defense_from_speakers(parsed_content),
            'witnesses': self._extract_witnesses_from_speakers(parsed_content),
            
            # Case parties
            'accused_persons': self._extract_accused_diarized(text, parsed_content),
            'complainant': self._extract_complainant_diarized(text, parsed_content),
            'victim': self._extract_victim_diarized(text, parsed_content),
            
            # Legal specifics
            'fir_details': self._extract_fir_diarized(text, parsed_content),
            'police_station': self._extract_police_station_diarized(text, parsed_content),
            'legal_sections': self._extract_legal_sections_diarized(text, parsed_content),
            
            # Content from speakers
            'judge_observations': self._extract_judge_content(parsed_content),
            'prosecutor_arguments': self._extract_prosecutor_content(parsed_content),
            'defense_arguments': self._extract_defense_content(parsed_content),
            'witness_testimony': self._extract_witness_content(parsed_content),
            'case_law_cited': self._extract_case_law_diarized(text, parsed_content),
            
            # Proceedings
            'evidence_presented': self._extract_evidence_diarized(text, parsed_content),
            'key_facts': self._extract_key_facts_diarized(text, parsed_content),
            'next_date': self._extract_next_date_diarized(text),
            'judgment_status': self._extract_judgment_status_diarized(text)
        }
        
        return info
    
    def _extract_case_name_diarized(self, text: str) -> str:
        """Extract case name from diarized transcript"""
        
        patterns = [
            r'Sessions Case No\. \d+/\d+\s*\n([^\n]+)',
            r'Civil Suit No\. \d+/\d+\s*\n([^\n]+)',
            r'Matrimonial Case No\. \d+/\d+\s*\n([^\n]+)',
            r'State v\. ([^\n]+)',
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+)',
            r'In the matter of ([^\n]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                if len(match.groups()) == 2:  # Two parties
                    return f"{match.group(1).strip()} v. {match.group(2).strip()}"
                else:
                    return match.group(1).strip()
        
        return "Case name not identified"
    
    def _extract_case_number_diarized(self, text: str) -> str:
        """Extract case number from diarized transcript"""
        
        patterns = [
            r'Sessions Case No\. (\d+/\d+)',
            r'Civil Suit No\. (\d+/\d+)',
            r'Matrimonial Case No\. (\d+/\d+)',
            r'Criminal Appeal No\. (\d+/\d+)',
            r'Case No\. (\d+/\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                case_type = pattern.split('\\')[0].replace('(', '').strip()
                return f"{case_type} {match.group(1)}"
        
        return "Case number not identified"
    
    def _extract_court_name_diarized(self, text: str) -> str:
        """Extract court name from diarized transcript"""
        
        patterns = [
            r'IN THE COURT OF ([^\n]+)',
            r'IN THE ([A-Z\s]+COURT[^\n]*)',
            r'FAMILY COURT[^\n]*',
            r'SESSIONS JUDGE[^\n]*'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                court_name = match.group(0).strip()
                return re.sub(r'\s+', ' ', court_name)
        
        return "Court not identified"
    
    def _extract_judge_name_diarized(self, text: str) -> str:
        """Extract judge name from diarized transcript"""
        
        patterns = [
            r'Hon\'ble Shri Justice ([A-Za-z\s\.]+)',
            r'Hon\'ble Ms\. Justice ([A-Za-z\s\.]+)',
            r'Hon\'ble Mr\. Justice ([A-Za-z\s\.]+)',
            r'Coram: Hon\'ble ([^\n]+)',
            r'Sessions Judge[:\s]*([A-Za-z\s\.]+)',
            r'Family Court Judge[:\s]*([A-Za-z\s\.]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                judge_name = match.group(1).strip()
                return re.sub(r'\s+', ' ', judge_name)
        
        return "Judge not identified"
    
    def _extract_hearing_date_diarized(self, text: str) -> str:
        """Extract hearing date from diarized transcript"""
        
        patterns = [
            r'Date:\s*([^\n]+)',
            r'Date of Hearing:\s*([^\n]+)',
            r'(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return "Date not specified"

    def _extract_prosecutor_from_speakers(self, parsed_content: Dict) -> str:
        """Extract prosecutor information from speaker labels"""

        # Look for prosecutor in speaker mapping
        for speaker, speaker_type in parsed_content['speaker_mapping'].items():
            if speaker_type == 'prosecutor':
                return f"Shri {speaker}, Public Prosecutor"

        # Fallback to content analysis
        prosecutor_statements = parsed_content.get('prosecutor_statements', [])
        if prosecutor_statements:
            return "Public Prosecutor (from transcript)"

        return "Public Prosecutor not identified"

    def _extract_defense_from_speakers(self, parsed_content: Dict) -> List[str]:
        """Extract defense advocates from speaker labels"""

        advocates = []

        # Look for advocates in speaker mapping
        for speaker, speaker_type in parsed_content['speaker_mapping'].items():
            if speaker_type == 'defense':
                if 'ADVOCATE' in speaker:
                    # Clean up advocate name
                    advocate_name = speaker.replace('ADVOCATE_', '').replace('_', ' ')
                    advocates.append(f"Advocate {advocate_name}")
                else:
                    advocates.append(f"Advocate {speaker}")

        return advocates if advocates else ["Defense advocates not identified"]

    def _extract_witnesses_from_speakers(self, parsed_content: Dict) -> List[str]:
        """Extract witnesses from speaker labels"""

        witnesses = []

        # Look for witnesses in speaker mapping
        for speaker, speaker_type in parsed_content['speaker_mapping'].items():
            if speaker_type == 'witness':
                # Skip common non-witness speakers
                if speaker not in ['JUDGE', 'PUBLIC_PROSECUTOR', 'COURT_CLERK']:
                    witness_name = speaker.replace('_', ' ')
                    witnesses.append(witness_name)

        return witnesses if witnesses else ["Witnesses not identified"]

    def _extract_accused_diarized(self, text: str, parsed_content: Dict) -> List[str]:
        """Extract accused persons from diarized content"""

        accused = []

        # Look in prosecutor and defense statements
        all_statements = (parsed_content.get('prosecutor_statements', []) +
                         parsed_content.get('defense_statements', []))

        patterns = [
            r'accused ([A-Za-z\s]+)',
            r'Accused No\. \d+ \(([A-Za-z\s]+)\)',
            r'my client ([A-Za-z\s]+)',
            r'defendant ([A-Za-z\s]+)'
        ]

        for statement in all_statements:
            for pattern in patterns:
                matches = re.findall(pattern, statement, re.IGNORECASE)
                for match in matches:
                    accused_name = match.strip()
                    if len(accused_name) > 2 and accused_name not in accused:
                        accused.append(accused_name)

        return accused if accused else ["Accused not identified"]

    def _extract_complainant_diarized(self, text: str, parsed_content: Dict) -> str:
        """Extract complainant from diarized content"""

        patterns = [
            r'complainant ([A-Za-z\s]+)',
            r'Complainant:\s*([A-Za-z\s]+)',
            r'complaint.*?([A-Za-z\s]+), father'
        ]

        # Check prosecutor statements first
        prosecutor_statements = parsed_content.get('prosecutor_statements', [])
        for statement in prosecutor_statements:
            for pattern in patterns:
                match = re.search(pattern, statement, re.IGNORECASE)
                if match:
                    return f"Shri {match.group(1).strip()}"

        return "Complainant not identified"

    def _extract_victim_diarized(self, text: str, parsed_content: Dict) -> str:
        """Extract victim from diarized content"""

        patterns = [
            r'victim ([A-Za-z\s]+)',
            r'deceased ([A-Za-z\s]+)',
            r'Victim:\s*([A-Za-z\s]+)'
        ]

        # Check prosecutor statements
        prosecutor_statements = parsed_content.get('prosecutor_statements', [])
        for statement in prosecutor_statements:
            for pattern in patterns:
                match = re.search(pattern, statement, re.IGNORECASE)
                if match:
                    name = match.group(1).strip()
                    return f"Shri/Smt. {name}"

        return "Victim not identified"

    def _extract_fir_diarized(self, text: str, parsed_content: Dict) -> Dict:
        """Extract FIR details from diarized content"""

        fir_info = {}

        # Look in prosecutor statements
        prosecutor_statements = parsed_content.get('prosecutor_statements', [])

        for statement in prosecutor_statements:
            # FIR number
            fir_match = re.search(r'FIR No\. (\d+/\d+)', statement)
            if fir_match:
                fir_info['fir_number'] = fir_match.group(1)

            # Sections
            sections_match = re.search(r'under Sections ([^.]+)', statement)
            if sections_match:
                fir_info['sections'] = sections_match.group(1).strip()

        return fir_info

    def _extract_police_station_diarized(self, text: str, parsed_content: Dict) -> str:
        """Extract police station from diarized content"""

        patterns = [
            r'Police Station ([A-Za-z\s]+)',
            r'PS ([A-Za-z\s]+)',
            r'posted at ([A-Za-z\s]+) Police Station'
        ]

        # Check witness statements (police officers)
        witness_statements = parsed_content.get('witness_statements', [])
        for statement in witness_statements:
            for pattern in patterns:
                match = re.search(pattern, statement, re.IGNORECASE)
                if match:
                    return match.group(1).strip()

        return "Police Station not identified"

    def _extract_legal_sections_diarized(self, text: str, parsed_content: Dict) -> Dict:
        """Extract legal sections from diarized content"""

        sections = {
            'ipc_sections': [],
            'crpc_sections': [],
            'evidence_act_sections': [],
            'other_sections': []
        }

        # Check all statements
        all_statements = (parsed_content.get('prosecutor_statements', []) +
                         parsed_content.get('defense_statements', []) +
                         parsed_content.get('witness_statements', []))

        for statement in all_statements:
            # IPC sections
            ipc_matches = re.findall(r'Section (\d+[A-Za-z]*) (?:of the )?(?:Indian Penal Code|IPC)', statement, re.IGNORECASE)
            sections['ipc_sections'].extend(ipc_matches)

            # CrPC sections
            crpc_matches = re.findall(r'Section (\d+[A-Za-z]*) (?:of the )?(?:Criminal Procedure Code|CrPC)', statement, re.IGNORECASE)
            sections['crpc_sections'].extend(crpc_matches)

            # Evidence Act sections
            evidence_matches = re.findall(r'Section (\d+[A-Za-z]*) (?:of the )?Evidence Act', statement, re.IGNORECASE)
            sections['evidence_act_sections'].extend(evidence_matches)

        # Remove duplicates
        for key in sections:
            sections[key] = list(set(sections[key]))

        return sections

    def _extract_judge_content(self, parsed_content: Dict) -> List[str]:
        """Extract judge observations and questions"""

        judge_statements = parsed_content.get('judge_statements', [])
        observations = []

        for statement in judge_statements:
            # Filter out procedural statements
            if len(statement) > 20 and not any(phrase in statement.lower() for phrase in
                                             ['good morning', 'court is adjourned', 'all rise']):
                observations.append(statement[:200])  # Limit length

        return observations[:5] if observations else ["Judge observations not recorded"]

    def _extract_prosecutor_content(self, parsed_content: Dict) -> List[str]:
        """Extract prosecutor arguments and statements"""

        prosecutor_statements = parsed_content.get('prosecutor_statements', [])
        arguments = []

        for statement in prosecutor_statements:
            if len(statement) > 30:
                arguments.append(statement[:200])  # Limit length

        return arguments[:5] if arguments else ["Prosecutor arguments not recorded"]

    def _extract_defense_content(self, parsed_content: Dict) -> List[str]:
        """Extract defense arguments and statements"""

        defense_statements = parsed_content.get('defense_statements', [])
        arguments = []

        for statement in defense_statements:
            if len(statement) > 30:
                arguments.append(statement[:200])  # Limit length

        return arguments[:5] if arguments else ["Defense arguments not recorded"]

    def _extract_witness_content(self, parsed_content: Dict) -> List[str]:
        """Extract witness testimony"""

        witness_statements = parsed_content.get('witness_statements', [])
        testimony = []

        for statement in witness_statements:
            if len(statement) > 30:
                testimony.append(statement[:200])  # Limit length

        return testimony[:5] if testimony else ["Witness testimony not recorded"]

    def _extract_case_law_diarized(self, text: str, parsed_content: Dict) -> List[str]:
        """Extract case law citations from diarized content"""

        cases = []

        # Check defense statements (most likely to cite cases)
        defense_statements = parsed_content.get('defense_statements', [])

        pattern = r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+), \((\d{4})\) (\d+) SCC (\d+)'

        for statement in defense_statements:
            matches = re.findall(pattern, statement)
            for match in matches:
                case_citation = f"{match[0].strip()} v. {match[1].strip()}, ({match[2]}) {match[3]} SCC {match[4]}"
                if case_citation not in cases:
                    cases.append(case_citation)

        return cases if cases else ["No case law cited"]

    def _extract_evidence_diarized(self, text: str, parsed_content: Dict) -> List[str]:
        """Extract evidence from diarized content"""

        evidence = []

        # Check prosecutor and witness statements
        relevant_statements = (parsed_content.get('prosecutor_statements', []) +
                              parsed_content.get('witness_statements', []))

        evidence_patterns = [
            r'recovered ([^.]+)',
            r'evidence ([^.]+)',
            r'CCTV ([^.]+)',
            r'forensic ([^.]+)',
            r'medical ([^.]+)',
            r'post-mortem ([^.]+)'
        ]

        for statement in relevant_statements:
            for pattern in evidence_patterns:
                matches = re.findall(pattern, statement, re.IGNORECASE)
                for match in matches:
                    if len(match) > 10:
                        evidence.append(match.strip())

        return evidence[:5] if evidence else ["Evidence details not specified"]

    def _extract_key_facts_diarized(self, text: str, parsed_content: Dict) -> List[str]:
        """Extract key facts from diarized content"""

        facts = []

        # Check prosecutor statements for key facts
        prosecutor_statements = parsed_content.get('prosecutor_statements', [])

        fact_patterns = [
            r'On ([^.]+), we received',
            r'During investigation ([^.]+)',
            r'The deceased ([^.]+)',
            r'The complainant ([^.]+)'
        ]

        for statement in prosecutor_statements:
            for pattern in fact_patterns:
                matches = re.findall(pattern, statement, re.IGNORECASE)
                for match in matches:
                    if len(match) > 15:
                        facts.append(match.strip())

        return facts[:5] if facts else ["Key facts not identified"]

    def _extract_next_date_diarized(self, text: str) -> str:
        """Extract next hearing date from diarized content"""

        patterns = [
            r'adjourned.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})',
            r'resume at.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})',
            r'posted.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return "Next date not specified"

    def _extract_judgment_status_diarized(self, text: str) -> str:
        """Extract judgment status from diarized content"""

        status_indicators = [
            ('judgment.*?reserved', 'Judgment Reserved'),
            ('arguments.*?concluded', 'Arguments Concluded'),
            ('matter.*?heard', 'Arguments Heard'),
            ('court.*?adjourned', 'Proceedings Adjourned'),
            ('examination.*?witnesses', 'Witness Examination Ongoing')
        ]

        text_lower = text.lower()
        for indicator, status in status_indicators:
            if re.search(indicator, text_lower):
                return status

        return "Proceedings ongoing"

# -----------------------------------------------------------------------------
# Diarized Summary Generator
# -----------------------------------------------------------------------------
class DiarizedSummaryGenerator:
    """Generate summaries from diarized content"""

    def __init__(self, case_type: str):
        self.case_type = case_type

    def generate_diarized_summary(self, case_info: Dict, original_text: str) -> str:
        """Generate comprehensive summary from diarized transcript"""

        # Generate narrative summary
        narrative_summary = self._generate_diarized_narrative(case_info)

        # Generate structured analysis
        structured_analysis = self._generate_diarized_structured_analysis(case_info)

        # Combine in professional format
        summary = f"""{'='*100}
DIARIZED INDIAN LEGAL CASE ANALYSIS - COMPREHENSIVE FORMAT
{'='*100}

EXECUTIVE SUMMARY (कार्यकारी सारांश):
{'-'*60}
{narrative_summary}

{'='*100}
DETAILED STRUCTURED ANALYSIS (विस्तृत संरचित विश्लेषण)
{'='*100}
{structured_analysis}

{'='*100}
END OF ANALYSIS (विश्लेषण समाप्त)
{'='*100}"""

        return summary

    def _generate_diarized_narrative(self, case_info: Dict) -> str:
        """Generate narrative summary from diarized case information"""

        narrative_parts = []

        # Case introduction
        case_name = case_info.get('case_name', '')
        case_number = case_info.get('case_number', '')
        court_name = case_info.get('court_name', '')
        judge_name = case_info.get('judge_name', '')
        hearing_date = case_info.get('hearing_date', '')

        if case_name and 'not identified' not in case_name:
            intro = f"In the matter of {case_name}"
            if case_number and 'not identified' not in case_number:
                intro += f" ({case_number})"
            narrative_parts.append(intro)

        if court_name and 'not identified' not in court_name:
            court_info = f"before the {court_name}"
            if judge_name and 'not identified' not in judge_name:
                court_info += f", presided over by Hon'ble {judge_name}"
            if hearing_date and 'not specified' not in hearing_date:
                court_info += f" on {hearing_date}"
            narrative_parts.append(court_info)

        # Personnel
        prosecutor = case_info.get('public_prosecutor', '')
        defense_advocates = case_info.get('defense_advocates', [])

        if prosecutor and 'not identified' not in prosecutor:
            narrative_parts.append(f"The case was argued by {prosecutor} for the State")

        if defense_advocates and 'not identified' not in str(defense_advocates):
            advocates_str = ', '.join(defense_advocates)
            narrative_parts.append(f"while the accused were represented by {advocates_str}")

        # Key proceedings
        prosecutor_args = case_info.get('prosecutor_arguments', [])
        defense_args = case_info.get('defense_arguments', [])

        if prosecutor_args and 'not recorded' not in str(prosecutor_args):
            narrative_parts.append(f"The prosecution argued that {prosecutor_args[0][:100]}...")

        if defense_args and 'not recorded' not in str(defense_args):
            narrative_parts.append(f"The defense contended that {defense_args[0][:100]}...")

        # Outcome
        judgment_status = case_info.get('judgment_status', '')
        next_date = case_info.get('next_date', '')

        if judgment_status and 'ongoing' not in judgment_status:
            outcome = f"The proceedings concluded with {judgment_status.lower()}"
            if next_date and 'not specified' not in next_date:
                outcome += f" and the matter was adjourned to {next_date}"
            narrative_parts.append(outcome)

        return ". ".join(narrative_parts) + "." if narrative_parts else "Case proceedings summary not available."

    def _generate_diarized_structured_analysis(self, case_info: Dict) -> str:
        """Generate structured analysis from diarized case information"""

        structure = f"""CASE IDENTIFICATION (मामले की पहचान):
Case Name: {case_info.get('case_name', 'Not identified')}
Case Number: {case_info.get('case_number', 'Not identified')}
Case Type: {case_info.get('case_type', 'Not specified').title()}
Court: {case_info.get('court_name', 'Not identified')}
Judge: {case_info.get('judge_name', 'Not identified')}
Hearing Date: {case_info.get('hearing_date', 'Not specified')}

LEGAL PERSONNEL (कानूनी कर्मचारी):
Public Prosecutor: {case_info.get('public_prosecutor', 'Not identified')}
Defense Advocates:
{self._format_list(case_info.get('defense_advocates', ['Not identified']))}
Witnesses Present:
{self._format_list(case_info.get('witnesses', ['Not identified']))}

PARTIES (पक्षकार):
Accused Persons:
{self._format_list(case_info.get('accused_persons', ['Not identified']))}
Complainant: {case_info.get('complainant', 'Not identified')}
Victim: {case_info.get('victim', 'Not identified')}

FIR DETAILS (एफआईआर विवरण):
{self._format_fir_details(case_info.get('fir_details', {}))}
Police Station: {case_info.get('police_station', 'Not identified')}

LEGAL PROVISIONS (कानूनी प्रावधान):
{self._format_legal_sections(case_info.get('legal_sections', {}))}

COURT PROCEEDINGS (न्यायालय की कार्यवाही):
Judge Observations:
{self._format_list(case_info.get('judge_observations', ['Not recorded']))}

Prosecutor Arguments:
{self._format_list(case_info.get('prosecutor_arguments', ['Not recorded']))}

Defense Arguments:
{self._format_list(case_info.get('defense_arguments', ['Not recorded']))}

Witness Testimony:
{self._format_list(case_info.get('witness_testimony', ['Not recorded']))}

EVIDENCE PRESENTED (प्रस्तुत साक्ष्य):
{self._format_list(case_info.get('evidence_presented', ['Not specified']))}

KEY FACTS (मुख्य तथ्य):
{self._format_list(case_info.get('key_facts', ['Not identified']))}

CASE LAW CITED (उद्धृत मामला कानून):
{self._format_list(case_info.get('case_law_cited', ['No case law cited']))}

JUDGMENT STATUS (निर्णय की स्थिति):
Status: {case_info.get('judgment_status', 'Proceedings ongoing')}
Next Date: {case_info.get('next_date', 'Not specified')}"""

        return structure

    def _format_list(self, items: List[str]) -> str:
        """Format list items for display"""
        if not items or (len(items) == 1 and any(phrase in items[0].lower() for phrase in ['not', 'none'])):
            return "• Not specified"
        return '\n'.join([f"• {item}" for item in items if item])

    def _format_fir_details(self, fir_details: Dict) -> str:
        """Format FIR details"""
        if not fir_details:
            return "• FIR details not available"

        details = []
        if fir_details.get('fir_number'):
            details.append(f"• FIR Number: {fir_details['fir_number']}")
        if fir_details.get('sections'):
            details.append(f"• Sections: {fir_details['sections']}")

        return '\n'.join(details) if details else "• FIR details not available"

    def _format_legal_sections(self, sections: Dict) -> str:
        """Format legal sections"""
        if not sections:
            return "• Legal sections not specified"

        formatted = []
        if sections.get('ipc_sections'):
            formatted.append(f"IPC Sections: {', '.join(sections['ipc_sections'])}")
        if sections.get('crpc_sections'):
            formatted.append(f"CrPC Sections: {', '.join(sections['crpc_sections'])}")
        if sections.get('evidence_act_sections'):
            formatted.append(f"Evidence Act Sections: {', '.join(sections['evidence_act_sections'])}")

        return '\n'.join([f"• {item}" for item in formatted]) if formatted else "• Legal sections not specified"

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------
def main():
    logger.info("Starting Diarized Indian Legal Summarizer...")

    # Read input
    try:
        with open(args.input, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        logger.error(f"Could not read input file: {e}")
        return

    logger.info(f"Input text length: {len(text)} characters")

    # Detect case type (simplified for diarized content)
    case_type = 'criminal'  # Default, can be enhanced
    if 'civil suit' in text.lower() or 'plaintiff' in text.lower():
        case_type = 'civil'
    elif 'matrimonial' in text.lower() or 'divorce' in text.lower():
        case_type = 'family'

    logger.info(f"Detected case type: {case_type}")

    # Extract information from diarized transcript
    extractor = DiarizedIndianLegalExtractor(case_type)
    case_info = extractor.extract_diarized_case_info(text)

    # Generate summary
    generator = DiarizedSummaryGenerator(case_type)
    summary = generator.generate_diarized_summary(case_info, text)

    # Write output
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error(f"Could not write output file: {e}")
        return

    logger.info(f"Diarized summary written to {args.output}")
    logger.info(f"Summary length: {len(summary)} characters")

    print(summary)

if __name__ == "__main__":
    main()
