#!/usr/bin/env python3
"""
universal_legal_summarizer.py

Universal legal document summarization system that automatically detects
case type and adapts summarization approach for all court proceedings:

- Criminal cases (homicide, assault, theft, drug, DUI, etc.)
- Civil cases (contract, personal injury, property, employment, etc.)
- Family court (divorce, custody, adoption)
- Bankruptcy, immigration, appeals, administrative hearings

Usage:
  python universal_legal_summarizer.py --input proceedings.txt --output summary.txt
"""

import argparse, logging, sys, re
from transformers import AutoTokenizer, BartForConditionalGeneration
import torch
from typing import Dict, List, Tuple

# -----------------------------------------------------------------------------
# Arguments
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Universal Legal Summarizer")
parser.add_argument("-i","--input", required=True, help="Input transcript file")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-output", type=int, default=800, help="Max summary length")
parser.add_argument("--case-type", help="Override auto-detection (criminal, civil, family, bankruptcy, immigration, appeals)")
args = parser.parse_args()

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# Case Type Detection
# -----------------------------------------------------------------------------
class CaseTypeDetector:
    """Automatically detect the type of legal case from transcript content"""
    
    def __init__(self):
        self.case_patterns = {
            'criminal': {
                'keywords': ['defendant', 'prosecution', 'guilty', 'not guilty', 'charges', 'arrest', 'miranda', 
                           'evidence', 'witness', 'testimony', 'verdict', 'sentencing', 'bail', 'plea'],
                'charges': ['homicide', 'murder', 'assault', 'theft', 'robbery', 'burglary', 'drug', 'dui', 
                          'domestic violence', 'fraud', 'embezzlement', 'battery', 'kidnapping'],
                'phrases': ['state v\\.', 'people v\\.', 'commonwealth v\\.', 'criminal case', 'felony', 'misdemeanor']
            },
            'civil': {
                'keywords': ['plaintiff', 'defendant', 'damages', 'liability', 'contract', 'breach', 'negligence',
                           'settlement', 'compensation', 'injunction', 'declaratory', 'summary judgment'],
                'types': ['personal injury', 'contract dispute', 'property dispute', 'employment', 'tort',
                         'malpractice', 'defamation', 'intellectual property', 'business dispute'],
                'phrases': ['civil action', 'civil case', 'complaint', 'counterclaim', 'cross-claim']
            },
            'family': {
                'keywords': ['custody', 'divorce', 'separation', 'alimony', 'child support', 'visitation',
                           'adoption', 'paternity', 'domestic relations', 'marital property', 'spousal support'],
                'types': ['dissolution', 'custody hearing', 'support modification', 'adoption proceeding'],
                'phrases': ['family court', 'domestic relations', 'in re marriage', 'custody matter']
            },
            'bankruptcy': {
                'keywords': ['debtor', 'creditor', 'discharge', 'liquidation', 'reorganization', 'trustee',
                           'assets', 'liabilities', 'chapter 7', 'chapter 11', 'chapter 13', 'automatic stay'],
                'types': ['chapter 7', 'chapter 11', 'chapter 13', 'adversary proceeding'],
                'phrases': ['bankruptcy court', 'in re:', 'debtor in possession']
            },
            'immigration': {
                'keywords': ['deportation', 'removal', 'asylum', 'refugee', 'visa', 'green card', 'citizenship',
                           'naturalization', 'immigration judge', 'ice', 'dhs', 'uscis'],
                'types': ['removal proceeding', 'asylum hearing', 'naturalization', 'visa application'],
                'phrases': ['immigration court', 'removal proceeding', 'asylum claim']
            },
            'appeals': {
                'keywords': ['appellant', 'appellee', 'appeal', 'reverse', 'affirm', 'remand', 'opinion',
                           'dissent', 'concur', 'precedent', 'standard of review', 'de novo'],
                'types': ['criminal appeal', 'civil appeal', 'administrative appeal'],
                'phrases': ['court of appeals', 'appellate court', 'supreme court', 'on appeal from']
            }
        }
    
    def detect_case_type(self, text: str) -> Tuple[str, float]:
        """Detect case type and return confidence score"""
        text_lower = text.lower()
        scores = {}
        
        for case_type, patterns in self.case_patterns.items():
            score = 0
            
            # Check keywords
            for keyword in patterns['keywords']:
                score += text_lower.count(keyword.lower()) * 2
            
            # Check specific types/charges
            if 'charges' in patterns:
                for charge in patterns['charges']:
                    score += text_lower.count(charge.lower()) * 3
            
            if 'types' in patterns:
                for case_subtype in patterns['types']:
                    score += text_lower.count(case_subtype.lower()) * 3
            
            # Check phrases (higher weight)
            for phrase in patterns['phrases']:
                matches = len(re.findall(phrase.lower(), text_lower))
                score += matches * 5
            
            scores[case_type] = score
        
        # Get highest scoring case type
        best_type = max(scores, key=scores.get)
        max_score = scores[best_type]
        
        # Calculate confidence (normalize by text length)
        confidence = min(max_score / (len(text.split()) / 100), 1.0)
        
        return best_type, confidence

# -----------------------------------------------------------------------------
# Universal Information Extractor
# -----------------------------------------------------------------------------
class UniversalInfoExtractor:
    """Extract information relevant to any type of legal case"""
    
    def __init__(self, case_type: str):
        self.case_type = case_type
    
    def extract_case_info(self, text: str) -> Dict:
        """Extract case information based on detected type"""
        
        base_info = self._extract_base_info(text)
        
        if self.case_type == 'criminal':
            return {**base_info, **self._extract_criminal_info(text)}
        elif self.case_type == 'civil':
            return {**base_info, **self._extract_civil_info(text)}
        elif self.case_type == 'family':
            return {**base_info, **self._extract_family_info(text)}
        elif self.case_type == 'bankruptcy':
            return {**base_info, **self._extract_bankruptcy_info(text)}
        elif self.case_type == 'immigration':
            return {**base_info, **self._extract_immigration_info(text)}
        elif self.case_type == 'appeals':
            return {**base_info, **self._extract_appeals_info(text)}
        else:
            return base_info
    
    def _extract_base_info(self, text: str) -> Dict:
        """Extract information common to all case types"""
        info = {
            'case_name': '',
            'case_number': '',
            'court': '',
            'judge': '',
            'date': '',
            'parties': [],
            'attorneys': [],
            'key_issues': [],
            'evidence': [],
            'testimony': [],
            'rulings': [],
            'next_steps': ''
        }
        
        # Case name and number
        case_match = re.search(r'([A-Z][a-z]+ v\. [A-Z][a-z]+|In re [A-Z][a-z]+)', text)
        if case_match:
            info['case_name'] = case_match.group(1)
        
        case_num_match = re.search(r'Case No\. ([\w\-]+)', text)
        if case_num_match:
            info['case_number'] = case_num_match.group(1)
        
        # Extract parties and attorneys
        info['parties'] = self._extract_parties(text)
        info['attorneys'] = self._extract_attorneys(text)
        
        # Extract dates
        date_patterns = [
            r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}'
        ]
        
        for pattern in date_patterns:
            dates = re.findall(pattern, text)
            if dates:
                info['date'] = dates[0]
                break
        
        return info
    
    def _extract_parties(self, text: str) -> List[str]:
        """Extract party names from text"""
        parties = []
        
        # Common party patterns
        party_patterns = [
            r'(Mr\.|Ms\.|Dr\.)\s+([A-Z][a-z]+)',
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s+sworn\.)',
            r'calls\s+([A-Z][a-z]+\s+[A-Z][a-z]+)'
        ]
        
        for pattern in party_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    party = ' '.join(match).strip()
                else:
                    party = match.strip()
                if party and party not in parties:
                    parties.append(party)
        
        return parties[:10]  # Limit to avoid noise
    
    def _extract_attorneys(self, text: str) -> List[str]:
        """Extract attorney names and roles"""
        attorneys = []
        
        attorney_patterns = [
            r'(PROSECUTOR|DEFENSE|PLAINTIFF|COUNSEL).*?([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)\s+(?:for the|representing)',
            r'Attorney\s+([A-Z][a-z]+\s+[A-Z][a-z]+)'
        ]
        
        for pattern in attorney_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    attorney = f"{match[0]}: {match[1]}"
                else:
                    attorney = match
                if attorney not in attorneys:
                    attorneys.append(attorney)
        
        return attorneys
    
    def _extract_criminal_info(self, text: str) -> Dict:
        """Extract criminal case specific information"""
        return {
            'charges': self._find_charges(text),
            'evidence_type': 'criminal_evidence',
            'key_elements': ['intent', 'evidence', 'witness_testimony', 'defendant_statements'],
            'verdict_status': self._find_verdict(text),
            'sentencing': self._find_sentencing(text)
        }
    
    def _extract_civil_info(self, text: str) -> Dict:
        """Extract civil case specific information"""
        return {
            'claim_type': self._find_civil_claims(text),
            'damages_sought': self._find_damages(text),
            'evidence_type': 'civil_evidence',
            'key_elements': ['liability', 'damages', 'causation', 'breach'],
            'settlement_status': self._find_settlement(text)
        }
    
    def _extract_family_info(self, text: str) -> Dict:
        """Extract family court specific information"""
        return {
            'family_matter_type': self._find_family_matter(text),
            'children_involved': self._find_children_info(text),
            'evidence_type': 'family_evidence',
            'key_elements': ['best_interests', 'custody', 'support', 'visitation'],
            'orders_requested': self._find_family_orders(text)
        }
    
    def _extract_bankruptcy_info(self, text: str) -> Dict:
        """Extract bankruptcy specific information"""
        return {
            'chapter_type': self._find_bankruptcy_chapter(text),
            'assets_liabilities': self._find_financial_info(text),
            'evidence_type': 'financial_evidence',
            'key_elements': ['debts', 'assets', 'discharge', 'reorganization'],
            'trustee_actions': self._find_trustee_actions(text)
        }
    
    def _extract_immigration_info(self, text: str) -> Dict:
        """Extract immigration case specific information"""
        return {
            'immigration_matter': self._find_immigration_matter(text),
            'country_of_origin': self._find_country(text),
            'evidence_type': 'immigration_evidence',
            'key_elements': ['persecution', 'credibility', 'country_conditions', 'eligibility'],
            'relief_sought': self._find_immigration_relief(text)
        }
    
    def _extract_appeals_info(self, text: str) -> Dict:
        """Extract appeals case specific information"""
        return {
            'appeal_type': self._find_appeal_type(text),
            'lower_court_ruling': self._find_lower_court_info(text),
            'evidence_type': 'appellate_record',
            'key_elements': ['standard_of_review', 'legal_error', 'precedent', 'jurisdiction'],
            'disposition': self._find_appellate_disposition(text)
        }
    
    # Helper methods for specific extractions
    def _find_charges(self, text: str) -> List[str]:
        charges = []
        charge_patterns = [
            r'charged with ([^.]+)',
            r'count \d+[:\-]\s*([^.]+)',
            r'guilty of ([^.]+)'
        ]
        
        for pattern in charge_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            charges.extend(matches)
        
        return charges[:5]  # Limit results
    
    def _find_verdict(self, text: str) -> str:
        verdict_patterns = [
            r'verdict of ([^.]+)',
            r'found (guilty|not guilty)',
            r'jury finds ([^.]+)'
        ]
        
        for pattern in verdict_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "pending"
    
    def _find_sentencing(self, text: str) -> str:
        sentence_patterns = [
            r'sentenced to ([^.]+)',
            r'sentence of ([^.]+)',
            r'imprisonment for ([^.]+)'
        ]
        
        for pattern in sentence_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "not yet sentenced"
    
    def _find_civil_claims(self, text: str) -> List[str]:
        claim_patterns = [
            r'claim for ([^.]+)',
            r'seeking ([^.]+)',
            r'damages for ([^.]+)'
        ]
        
        claims = []
        for pattern in claim_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            claims.extend(matches)
        
        return claims[:3]
    
    def _find_damages(self, text: str) -> str:
        damage_patterns = [
            r'\$[\d,]+',
            r'damages of ([^.]+)',
            r'compensation for ([^.]+)'
        ]
        
        for pattern in damage_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return "unspecified"
    
    def _find_settlement(self, text: str) -> str:
        if re.search(r'settlement|settled|agree', text, re.IGNORECASE):
            return "settlement discussed"
        return "no settlement mentioned"
    
    def _find_family_matter(self, text: str) -> str:
        family_types = ['divorce', 'custody', 'adoption', 'support', 'paternity']
        
        for matter_type in family_types:
            if matter_type in text.lower():
                return matter_type
        
        return "family matter"
    
    def _find_children_info(self, text: str) -> List[str]:
        child_patterns = [
            r'child(?:ren)?\s+([^.]+)',
            r'minor(?:s)?\s+([^.]+)'
        ]
        
        children = []
        for pattern in child_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            children.extend(matches)
        
        return children[:3]
    
    def _find_family_orders(self, text: str) -> List[str]:
        order_patterns = [
            r'order(?:s)?\s+([^.]+)',
            r'requesting ([^.]+)',
            r'seeking ([^.]+)'
        ]
        
        orders = []
        for pattern in order_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            orders.extend(matches)
        
        return orders[:3]
    
    def _find_bankruptcy_chapter(self, text: str) -> str:
        chapter_match = re.search(r'chapter\s+(\d+)', text, re.IGNORECASE)
        if chapter_match:
            return f"Chapter {chapter_match.group(1)}"
        return "unspecified chapter"
    
    def _find_financial_info(self, text: str) -> Dict:
        return {
            'assets': re.findall(r'assets?\s+([^.]+)', text, re.IGNORECASE)[:3],
            'debts': re.findall(r'debt(?:s)?\s+([^.]+)', text, re.IGNORECASE)[:3]
        }
    
    def _find_trustee_actions(self, text: str) -> List[str]:
        return re.findall(r'trustee\s+([^.]+)', text, re.IGNORECASE)[:3]
    
    def _find_immigration_matter(self, text: str) -> str:
        immigration_types = ['asylum', 'removal', 'deportation', 'naturalization', 'visa']
        
        for matter_type in immigration_types:
            if matter_type in text.lower():
                return matter_type
        
        return "immigration matter"
    
    def _find_country(self, text: str) -> str:
        # This would need a more comprehensive country list in practice
        countries = ['mexico', 'china', 'india', 'philippines', 'el salvador', 'guatemala', 'honduras']
        
        for country in countries:
            if country in text.lower():
                return country.title()
        
        return "unspecified"
    
    def _find_immigration_relief(self, text: str) -> str:
        relief_patterns = [
            r'seeking ([^.]+)',
            r'application for ([^.]+)',
            r'relief from ([^.]+)'
        ]
        
        for pattern in relief_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "unspecified relief"
    
    def _find_appeal_type(self, text: str) -> str:
        if 'criminal' in text.lower():
            return "criminal appeal"
        elif 'civil' in text.lower():
            return "civil appeal"
        else:
            return "appeal"
    
    def _find_lower_court_info(self, text: str) -> str:
        lower_court_patterns = [
            r'trial court ([^.]+)',
            r'lower court ([^.]+)',
            r'district court ([^.]+)'
        ]
        
        for pattern in lower_court_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "lower court ruling"
    
    def _find_appellate_disposition(self, text: str) -> str:
        dispositions = ['affirmed', 'reversed', 'remanded', 'dismissed']
        
        for disposition in dispositions:
            if disposition in text.lower():
                return disposition
        
        return "pending"

# -----------------------------------------------------------------------------
# Universal Summary Generator
# -----------------------------------------------------------------------------
class UniversalSummaryGenerator:
    """Generate summaries adapted to specific case types"""

    def __init__(self, case_type: str):
        self.case_type = case_type
        self.tokenizer = AutoTokenizer.from_pretrained("facebook/bart-large-cnn")
        self.model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")

    def generate_summary(self, case_info: Dict, original_text: str) -> str:
        """Generate case-type specific summary"""

        if self.case_type == 'criminal':
            return self._generate_criminal_summary(case_info, original_text)
        elif self.case_type == 'civil':
            return self._generate_civil_summary(case_info, original_text)
        elif self.case_type == 'family':
            return self._generate_family_summary(case_info, original_text)
        elif self.case_type == 'bankruptcy':
            return self._generate_bankruptcy_summary(case_info, original_text)
        elif self.case_type == 'immigration':
            return self._generate_immigration_summary(case_info, original_text)
        elif self.case_type == 'appeals':
            return self._generate_appeals_summary(case_info, original_text)
        else:
            return self._generate_generic_summary(case_info, original_text)

    def _generate_ai_summary(self, content: str, case_type: str) -> str:
        """Generate AI-enhanced summary of key content"""
        try:
            prompt = f"Summarize these {case_type}: {content[:800]}"

            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                max_length=1024,
                truncation=True
            )

            with torch.no_grad():
                summary_ids = self.model.generate(
                    inputs.input_ids,
                    max_length=150,
                    min_length=40,
                    num_beams=4,
                    length_penalty=2.0,
                    early_stopping=True
                )

            return self.tokenizer.decode(summary_ids[0], skip_special_tokens=True)
        except:
            return content[:200] + "..."

    def _generate_criminal_summary(self, case_info: Dict, text: str) -> str:
        """Generate criminal case summary"""

        header = f"""CRIMINAL CASE SUMMARY
Case: {case_info.get('case_name', 'Unknown')} {case_info.get('case_number', '')}
Charges: {', '.join(case_info.get('charges', ['Not specified']))}
Court Date: {case_info.get('date', 'Not specified')}

PARTIES:
- Prosecution: {self._get_attorney_by_role(case_info, 'PROSECUTOR')}
- Defense: {self._get_attorney_by_role(case_info, 'DEFENSE')}
- Defendant: {self._get_party_by_role(case_info, 'defendant')}"""

        # Generate AI summary for key proceedings
        key_content = self._extract_key_content(text, ['evidence', 'testimony', 'witness', 'forensic'])
        ai_summary = self._generate_ai_summary(key_content, "criminal case proceedings")

        evidence_section = f"""
EVIDENCE & TESTIMONY:
{ai_summary}

CHARGES & ALLEGATIONS:
{self._format_list(case_info.get('charges', []))}

VERDICT/STATUS:
{case_info.get('verdict_status', 'Pending')}

SENTENCING:
{case_info.get('sentencing', 'Not yet determined')}"""

        return header + evidence_section

    def _generate_civil_summary(self, case_info: Dict, text: str) -> str:
        """Generate civil case summary"""

        header = f"""CIVIL CASE SUMMARY
Case: {case_info.get('case_name', 'Unknown')} {case_info.get('case_number', '')}
Claim Type: {', '.join(case_info.get('claim_type', ['Not specified']))}
Court Date: {case_info.get('date', 'Not specified')}

PARTIES:
- Plaintiff: {self._get_party_by_role(case_info, 'plaintiff')}
- Defendant: {self._get_party_by_role(case_info, 'defendant')}
- Attorneys: {', '.join(case_info.get('attorneys', ['Not specified']))}"""

        key_content = self._extract_key_content(text, ['damages', 'liability', 'breach', 'contract'])
        ai_summary = self._generate_ai_summary(key_content, "civil case proceedings")

        claims_section = f"""
CLAIMS & ARGUMENTS:
{ai_summary}

DAMAGES SOUGHT:
{case_info.get('damages_sought', 'Not specified')}

SETTLEMENT STATUS:
{case_info.get('settlement_status', 'No settlement mentioned')}"""

        return header + claims_section

    def _generate_family_summary(self, case_info: Dict, text: str) -> str:
        """Generate family court summary"""

        header = f"""FAMILY COURT SUMMARY
Case: {case_info.get('case_name', 'Unknown')} {case_info.get('case_number', '')}
Matter Type: {case_info.get('family_matter_type', 'Family matter')}
Court Date: {case_info.get('date', 'Not specified')}

PARTIES:
- Petitioner: {self._get_party_by_role(case_info, 'petitioner')}
- Respondent: {self._get_party_by_role(case_info, 'respondent')}
- Children: {', '.join(case_info.get('children_involved', ['None specified']))}"""

        key_content = self._extract_key_content(text, ['custody', 'support', 'visitation', 'best interests'])
        ai_summary = self._generate_ai_summary(key_content, "family court proceedings")

        family_section = f"""
FAMILY ISSUES:
{ai_summary}

ORDERS REQUESTED:
{', '.join(case_info.get('orders_requested', ['Not specified']))}"""

        return header + family_section

    def _generate_generic_summary(self, case_info: Dict, text: str) -> str:
        """Generate generic legal summary for unknown case types"""

        header = f"""LEGAL PROCEEDING SUMMARY
Case: {case_info.get('case_name', 'Unknown')} {case_info.get('case_number', '')}
Court Date: {case_info.get('date', 'Not specified')}

PARTIES:
{self._format_parties(case_info.get('parties', []))}

ATTORNEYS:
{self._format_attorneys(case_info.get('attorneys', []))}"""

        key_content = self._extract_key_content(text, ['testimony', 'evidence', 'ruling', 'order'])
        ai_summary = self._generate_ai_summary(key_content, "legal proceedings")

        generic_section = f"""
PROCEEDINGS:
{ai_summary}

NEXT STEPS:
{case_info.get('next_steps', 'Not specified')}"""

        return header + generic_section

    # Helper methods
    def _get_attorney_by_role(self, case_info: Dict, role: str) -> str:
        attorneys = case_info.get('attorneys', [])
        for attorney in attorneys:
            if role.upper() in attorney.upper():
                return attorney.split(':')[-1].strip() if ':' in attorney else attorney
        return "Not specified"

    def _get_party_by_role(self, case_info: Dict, role: str) -> str:
        parties = case_info.get('parties', [])
        for party in parties:
            if role.lower() in party.lower():
                return party
        return "Not specified"

    def _extract_key_content(self, text: str, keywords: List[str]) -> str:
        sentences = text.split('.')
        key_sentences = []

        for sentence in sentences:
            if any(keyword.lower() in sentence.lower() for keyword in keywords):
                key_sentences.append(sentence.strip())

        return '. '.join(key_sentences[:10])  # Limit to 10 sentences

    def _format_list(self, items: List[str]) -> str:
        if not items:
            return "None specified"
        return '\n- ' + '\n- '.join(items)

    def _format_parties(self, parties: List[str]) -> str:
        if not parties:
            return "Not specified"
        return '\n- ' + '\n- '.join(parties)

    def _format_attorneys(self, attorneys: List[str]) -> str:
        if not attorneys:
            return "Not specified"
        return '\n- ' + '\n- '.join(attorneys)

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------
def main():
    logger.info("Starting Universal Legal Summarizer...")

    # Read input
    try:
        with open(args.input, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        logger.error(f"Could not read input file: {e}")
        sys.exit(1)

    logger.info(f"Input text length: {len(text)} characters")

    # Detect case type or use override
    if args.case_type:
        case_type = args.case_type.lower()
        confidence = 1.0
        logger.info(f"Using manual case type: {case_type}")
    else:
        detector = CaseTypeDetector()
        case_type, confidence = detector.detect_case_type(text)
        logger.info(f"Detected case type: {case_type} (confidence: {confidence:.2f})")

    # Extract case information
    extractor = UniversalInfoExtractor(case_type)
    case_info = extractor.extract_case_info(text)

    # Generate summary
    generator = UniversalSummaryGenerator(case_type)
    summary = generator.generate_summary(case_info, text)

    # Write output
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error(f"Could not write output file: {e}")
        sys.exit(1)

    logger.info(f"Universal summary written to {args.output}")
    logger.info(f"Summary length: {len(summary)} characters")

    print("\n" + "="*100)
    print(f"UNIVERSAL LEGAL SUMMARY - {case_type.upper()} CASE")
    print("="*100)
    print(summary)
    print("="*100)

if __name__ == "__main__":
    main()
