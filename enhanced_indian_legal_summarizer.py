#!/usr/bin/env python3
"""
enhanced_indian_legal_summarizer.py

ENHANCED INDIAN LEGAL SUMMARIZATION SYSTEM
Fixes identified issues:
- Text truncation problems
- Section classification accuracy (IPC vs CrPC vs Evidence Act)
- Improved narrative flow
- Better extraction patterns
- Meaningful structured analysis responses

Usage:
  python enhanced_indian_legal_summarizer.py --input proceedings.txt --output summary.txt
"""

import argparse, logging, sys, re
from transformers import AutoTokenizer, BartForConditionalGeneration
from typing import Dict, List, Tuple
import torch

# -----------------------------------------------------------------------------
# Arguments
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Enhanced Indian Legal Summarizer")
parser.add_argument("-i","--input", required=True, help="Input transcript file")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-narrative", type=int, default=400, help="Max narrative summary length")
args = parser.parse_args()

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# Enhanced Indian Case Type Detection
# -----------------------------------------------------------------------------
def detect_indian_case_type(text: str) -> Tuple[str, float]:
    """Enhanced Indian case type detection"""
    text_lower = text.lower()

    indian_patterns = {
        'criminal': {
            'keywords': ['criminal appeal', 'sessions case', 'fir', 'accused', 'police station',
                        'investigation', 'chargesheet', 'bail', 'remand', 'prosecution', 'pw-'],
            'sections': ['section 302', 'section 307', 'section 420', 'section 498a', 'section 376',
                        'section 363', 'section 365', 'section 368', 'ipc', 'indian penal code'],
            'courts': ['sessions judge', 'additional sessions judge', 'chief judicial magistrate', 'acjm'],
            'phrases': ['state v.', 'state of', 'criminal appeal no.', 'sessions case no.']
        },
        'civil': {
            'keywords': ['civil suit', 'civil appeal', 'plaintiff', 'defendant', 'damages', 'injunction',
                        'specific performance', 'declaration', 'possession', 'title suit', 'contract'],
            'sections': ['order 7', 'order 8', 'order 39', 'cpc', 'civil procedure code', 'specific relief act'],
            'courts': ['civil judge', 'district judge', 'civil court'],
            'phrases': ['civil suit no.', 'civil appeal no.', 'plaintiff v.', 'defendant no.']
        },
        'family': {
            'keywords': ['matrimonial', 'divorce', 'maintenance', 'custody', 'hindu marriage act',
                        'muslim personal law', 'domestic violence', 'dowry', 'alimony'],
            'sections': ['section 13', 'section 125 crpc', 'protection of women act', 'hindu marriage act'],
            'courts': ['family court', 'matrimonial court'],
            'phrases': ['matrimonial suit', 'divorce petition', 'custody matter']
        },
        'constitutional': {
            'keywords': ['writ petition', 'mandamus', 'certiorari', 'habeas corpus', 'fundamental rights',
                        'article 226', 'article 32', 'public interest litigation', 'pil'],
            'sections': ['article 14', 'article 19', 'article 21', 'constitution'],
            'courts': ['high court', 'supreme court'],
            'phrases': ['writ petition no.', 'pil no.', 'constitutional matter']
        }
    }

    scores = {}
    for case_type, patterns in indian_patterns.items():
        score = 0

        # Check keywords (weight: 2)
        for keyword in patterns['keywords']:
            score += text_lower.count(keyword) * 2

        # Check legal sections (weight: 3)
        for section in patterns['sections']:
            score += text_lower.count(section) * 3

        # Check court types (weight: 4)
        for court in patterns['courts']:
            score += text_lower.count(court) * 4

        # Check phrases (weight: 5)
        for phrase in patterns['phrases']:
            score += text_lower.count(phrase) * 5

        scores[case_type] = score

    best_type = max(scores, key=scores.get)
    confidence = min(scores[best_type] / 20, 1.0)  # Normalize

    return best_type, confidence

# -----------------------------------------------------------------------------
# Enhanced Information Extractor
# -----------------------------------------------------------------------------
class EnhancedIndianLegalExtractor:
    """Enhanced extractor with improved patterns and accuracy"""

    def __init__(self, case_type: str):
        self.case_type = case_type

    def extract_enhanced_case_info(self, text: str) -> Dict:
        """Extract comprehensive Indian case information with improved accuracy"""

        info = {
            # Case identification - improved extraction
            'case_name': self._extract_case_name_enhanced(text),
            'case_number': self._extract_case_number_enhanced(text),
            'case_type': self.case_type,
            'court_name': self._extract_court_name_enhanced(text),
            'judge_name': self._extract_judge_name_enhanced(text),
            'hearing_date': self._extract_hearing_date_enhanced(text),

            # Personnel - improved patterns
            'public_prosecutor': self._extract_public_prosecutor_enhanced(text),
            'defense_advocates': self._extract_defense_advocates_enhanced(text),
            'accused_persons': self._extract_accused_persons_enhanced(text),
            'complainant': self._extract_complainant_enhanced(text),
            'victim': self._extract_victim_enhanced(text),

            # Legal specifics - better classification
            'fir_details': self._extract_fir_details_enhanced(text),
            'police_station': self._extract_police_station_enhanced(text),
            'ipc_sections': self._extract_ipc_sections_enhanced(text),
            'crpc_sections': self._extract_crpc_sections_enhanced(text),
            'evidence_act_sections': self._extract_evidence_act_sections_enhanced(text),

            # Witnesses and evidence - improved extraction
            'prosecution_witnesses': self._extract_prosecution_witnesses_enhanced(text),
            'defense_witnesses': self._extract_defense_witnesses_enhanced(text),
            'case_law_cited': self._extract_case_law_enhanced(text),

            # Proceedings - meaningful responses
            'legal_arguments': self._extract_legal_arguments_enhanced(text),
            'court_observations': self._extract_court_observations_enhanced(text),
            'evidence_presented': self._extract_evidence_presented(text),
            'key_facts': self._extract_key_facts_enhanced(text),
            'next_date': self._extract_next_date_enhanced(text),
            'judgment_status': self._extract_judgment_status_enhanced(text)
        }

        return info

    def _extract_case_name_enhanced(self, text: str) -> str:
        """Enhanced case name extraction with better patterns"""
        patterns = [
            r'State \(NCT of Delhi\) v\. ([^\n\r]+?)(?:\s*\n|\s*$)',
            r'State of ([A-Za-z\s]+) v\. ([^\n\r]+?)(?:\s*\n|\s*$)',
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+) & Anr\.?',
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+)(?:\s*\n|\s*Case)',
            r'In the matter of ([^\n\r]+?)(?:\s*\n|\s*$)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                full_match = match.group(0).strip()
                # Clean up the match
                full_match = re.sub(r'\s+', ' ', full_match)
                return full_match

        return "Case name not identified"

    def _extract_case_number_enhanced(self, text: str) -> str:
        """Enhanced case number extraction"""
        patterns = [
            r'Criminal Appeal No\. (\d+/\d+)',
            r'Crl\. A\. No\. (\d+/\d+)',
            r'Sessions Case No\. (\d+/\d+)',
            r'Civil Suit No\. (\d+/\d+)',
            r'Civil Appeal No\. (\d+/\d+)',
            r'Writ Petition No\. (\d+/\d+)',
            r'Case No\. (\d+/\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                case_type = pattern.split('\\')[0].replace('(', '').strip()
                return f"{case_type} {match.group(1)}"

        return "Case number not identified"

    def _extract_court_name_enhanced(self, text: str) -> str:
        """Enhanced court name extraction without truncation"""
        patterns = [
            r'IN THE COURT OF ([^\n\r]+?)(?=\n[A-Z]|\n\n|\nCoram)',
            r'DELHI DISTRICT COURT, ([^\n\r]+?)(?=\n[A-Z]|\n\n)',
            r'HIGH COURT OF ([^\n\r]+?)(?=\n[A-Z]|\n\n)',
            r'SUPREME COURT OF INDIA'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                court_name = match.group(0).strip()
                # Clean up multiple spaces and newlines
                court_name = re.sub(r'\s+', ' ', court_name)
                return court_name

        return "Court not identified"

    def _extract_judge_name_enhanced(self, text: str) -> str:
        """Enhanced judge name extraction"""
        patterns = [
            r'Hon\'ble Shri Justice ([A-Za-z\s\.]+?)(?=\n|Additional|District)',
            r'Hon\'ble Mr\. Justice ([A-Za-z\s\.]+?)(?=\n|Additional|District)',
            r'Hon\'ble Ms\. Justice ([A-Za-z\s\.]+?)(?=\n|Additional|District)',
            r'Coram: Hon\'ble ([^\n\r]+?)(?=\n[A-Z]|\n\n)',
            r'Additional Sessions Judge[:\s]*([A-Za-z\s\.]+?)(?=\n|Date)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                judge_name = match.group(1).strip()
                # Clean up the name
                judge_name = re.sub(r'\s+', ' ', judge_name)
                return judge_name

        return "Judge not identified"

    def _extract_hearing_date_enhanced(self, text: str) -> str:
        """Enhanced hearing date extraction"""
        patterns = [
            r'Date of Hearing:\s*([^\n\r]+?)(?=\n|Date)',
            r'Hearing Date:\s*([^\n\r]+?)(?=\n)',
            r'Date:\s*(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})',
            r'(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                date = match.group(1).strip()
                return date

        return "Date not specified"

    def _extract_public_prosecutor_enhanced(self, text: str) -> str:
        """Enhanced public prosecutor extraction"""
        patterns = [
            r'Shri ([A-Za-z\s]+), Additional Public Prosecutor',
            r'Ms\. ([A-Za-z\s]+), Additional Public Prosecutor',
            r'ADDITIONAL PUBLIC PROSECUTOR \(Shri ([A-Za-z\s]+)\)',
            r'Present:.*?Shri ([A-Za-z\s]+), Additional Public Prosecutor',
            r'APP.*?Shri ([A-Za-z\s]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                name = match.group(1).strip()
                return f"Shri {name}, Additional Public Prosecutor"

        return "Public Prosecutor not identified"

    def _extract_defense_advocates_enhanced(self, text: str) -> List[str]:
        """Enhanced defense advocate extraction"""
        patterns = [
            r'Shri Advocate ([A-Za-z\s]+) for Accused No\. (\d+)',
            r'Ms\. Advocate ([A-Za-z\s]+) for Accused No\. (\d+)',
            r'Present:.*?Shri Advocate ([A-Za-z\s]+) for Accused',
            r'Present:.*?Ms\. Advocate ([A-Za-z\s]+) for Accused',
            r'ADVOCATE ([A-Za-z\s]+): Much obliged'
        ]

        advocates = []
        seen_advocates = set()

        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) == 2:  # Name and accused number
                        advocate_info = f"Advocate {match[0].strip()} for Accused No. {match[1]}"
                    else:
                        advocate_info = f"Advocate {match[0].strip()}"
                else:
                    advocate_info = f"Advocate {match.strip()}"

                if advocate_info not in seen_advocates:
                    advocates.append(advocate_info)
                    seen_advocates.add(advocate_info)

        return advocates if advocates else ["Defense advocates not identified"]

    def _extract_accused_persons_enhanced(self, text: str) -> List[str]:
        """Enhanced accused persons extraction"""
        patterns = [
            r'Accused No\. \d+ \(([A-Za-z\s]+)\)',
            r'accused ([A-Za-z\s]+) disclosed',
            r'my client ([A-Za-z\s]+) was',
            r'defendant ([A-Za-z\s]+)',
            r'Accused:\s*\d+\.\s*([A-Za-z\s]+)'
        ]

        accused = []
        seen_accused = set()

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                accused_name = match.strip()
                if accused_name and accused_name not in seen_accused and len(accused_name) > 2:
                    accused.append(accused_name)
                    seen_accused.add(accused_name)

        return accused if accused else ["Accused not identified"]

    def _extract_complainant_enhanced(self, text: str) -> str:
        """Enhanced complainant extraction"""
        patterns = [
            r'complainant Shri ([A-Za-z\s]+)',
            r'Complainant:\s*Shri ([A-Za-z\s]+)',
            r'complaint from ([A-Za-z\s]+), father',
            r'written complaint from the complainant ([A-Za-z\s]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                return f"Shri {name}"

        return "Complainant not identified"

    def _extract_victim_enhanced(self, text: str) -> str:
        """Enhanced victim extraction"""
        patterns = [
            r'victim,? Smt\. ([A-Za-z\s]+)',
            r'Victim:\s*Smt\. ([A-Za-z\s]+)',
            r'the victim ([A-Za-z\s]+) was',
            r'found ([A-Za-z\s]+) lying'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                name = match.group(1).strip()
                return f"Smt. {name}"

        return "Victim not identified"

    def _extract_fir_details_enhanced(self, text: str) -> Dict:
        """Enhanced FIR details extraction"""
        fir_info = {}

        # FIR Number
        fir_patterns = [
            r'FIR No\. (\d+/\d+)',
            r'F\.I\.R\. No\. (\d+/\d+)',
            r'First Information Report No\. (\d+/\d+)'
        ]

        for pattern in fir_patterns:
            match = re.search(pattern, text)
            if match:
                fir_info['fir_number'] = match.group(1)
                break

        # FIR registration sections
        reg_patterns = [
            r'FIR was registered under Sections ([^.]+)',
            r'registered under Sections ([^.]+) IPC',
            r'under Sections ([^.]+) of.*?Indian Penal Code'
        ]

        for pattern in reg_patterns:
            match = re.search(pattern, text)
            if match:
                sections = match.group(1).strip()
                fir_info['sections'] = sections
                break

        return fir_info

    def _extract_police_station_enhanced(self, text: str) -> str:
        """Enhanced police station extraction"""
        patterns = [
            r'PS ([A-Za-z\s]+) has deposed',
            r'Police Station:\s*([A-Za-z\s]+)',
            r'of PS ([A-Za-z\s]+)',
            r'Police Station ([A-Za-z\s]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                ps_name = match.group(1).strip()
                return ps_name

        return "Police Station not identified"

    def _extract_ipc_sections_enhanced(self, text: str) -> List[str]:
        """Enhanced IPC sections extraction with better classification"""
        # Only extract sections explicitly mentioned with IPC
        patterns = [
            r'Section (\d+[A-Za-z]*) (?:of the )?(?:Indian Penal Code|IPC)',
            r'Sections ([0-9, ]+) (?:of the )?(?:Indian Penal Code|IPC)',
            r'under Section (\d+[A-Za-z]*) IPC',
            r'(\d+), (\d+), and (\d+) IPC'  # Multiple sections
        ]

        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    sections.extend([s.strip() for s in match if s.strip()])
                else:
                    # Handle comma-separated sections
                    if ',' in match:
                        sections.extend([s.strip() for s in match.split(',') if s.strip()])
                    else:
                        sections.append(match.strip())

        # Remove duplicates and filter out CrPC sections
        ipc_sections = []
        crpc_sections = ['161', '164', '125', '41', '154']  # Common CrPC sections

        for section in set(sections):
            if section not in crpc_sections:
                ipc_sections.append(section)

        return ipc_sections if ipc_sections else ["IPC sections not specified"]

    def _extract_crpc_sections_enhanced(self, text: str) -> List[str]:
        """Enhanced CrPC sections extraction"""
        patterns = [
            r'Section (\d+[A-Za-z]*) (?:of the )?(?:Criminal Procedure Code|CrPC)',
            r'under Section (\d+[A-Za-z]*) CrPC',
            r'Section (\d+[A-Za-z]*) Cr\.P\.C\.',
            r'interrogation under Section (\d+[A-Za-z]*)',  # Common for 161 CrPC
            r'statement under Section (\d+[A-Za-z]*)'  # Common for 164 CrPC
        ]

        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            sections.extend(matches)

        return list(set(sections)) if sections else ["CrPC sections not specified"]

    def _extract_evidence_act_sections_enhanced(self, text: str) -> List[str]:
        """Enhanced Evidence Act sections extraction"""
        patterns = [
            r'Section (\d+[A-Za-z]*) of the Evidence Act',
            r'under Section (\d+[A-Za-z]*) Evidence Act',
            r'Section (\d+[A-Za-z]*) of.*?Indian Evidence Act'
        ]

        sections = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            sections.extend(matches)

        return list(set(sections)) if sections else ["Evidence Act sections not specified"]

    def _extract_prosecution_witnesses_enhanced(self, text: str) -> List[str]:
        """Enhanced prosecution witnesses extraction"""
        patterns = [
            r'PW-(\d+) ([A-Za-z\s\.]+?) (?:of PS|from|has deposed|examined)',
            r'PW-(\d+):? ([A-Za-z\s\.]+)',
            r'prosecution witness.*?([A-Za-z\s\.]+)',
            r'The State calls ([A-Za-z\s\.]+)'
        ]

        witnesses = []
        seen_witnesses = set()

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple) and len(match) == 2:
                    witness_info = f"PW-{match[0]} {match[1].strip()}"
                else:
                    witness_info = f"Prosecution Witness: {match.strip()}"

                if witness_info not in seen_witnesses:
                    witnesses.append(witness_info)
                    seen_witnesses.add(witness_info)

        return witnesses if witnesses else ["Prosecution witnesses not identified"]

    def _extract_defense_witnesses_enhanced(self, text: str) -> List[str]:
        """Enhanced defense witnesses extraction"""
        patterns = [
            r'DW-(\d+) ([A-Za-z\s]+)',
            r'defense witness ([A-Za-z\s]+)',
            r'The defense calls ([A-Za-z\s]+)'
        ]

        witnesses = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    witnesses.append(f"DW-{match[0]} {match[1]}")
                else:
                    witnesses.append(f"Defense witness: {match}")

        return witnesses if witnesses else ["Defense witnesses not identified"]

    def _extract_case_law_enhanced(self, text: str) -> List[str]:
        """Enhanced case law extraction with noise reduction"""
        patterns = [
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+), \((\d{4})\) (\d+) SCC (\d+)',
            r'in ([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+), \((\d{4})\)',
            r'([A-Za-z\s\.]+) v\. ([A-Za-z\s\.]+) \((\d{4})\)'
        ]

        cases = []
        seen_cases = set()

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) == 5:  # Full SCC citation
                    case_citation = f"{match[0].strip()} v. {match[1].strip()}, ({match[2]}) {match[3]} SCC {match[4]}"
                elif len(match) == 3:  # Year only
                    case_citation = f"{match[0].strip()} v. {match[1].strip()} ({match[2]})"
                else:
                    continue

                # Filter out noise (very short names, numbers, etc.)
                if len(match[0].strip()) > 3 and len(match[1].strip()) > 3:
                    if case_citation not in seen_cases:
                        cases.append(case_citation)
                        seen_cases.add(case_citation)

        return cases if cases else ["No case law cited"]

    def _extract_legal_arguments_enhanced(self, text: str) -> List[str]:
        """Enhanced legal arguments extraction with meaningful content"""
        arguments = []

        # Extract advocate arguments with context
        advocate_patterns = [
            r'ADVOCATE [A-Za-z\s]+: ([^\\n]+(?:\\n[^A-Z][^\\n]*)*)',
            r'learned counsel.*?: ([^\\n]+)',
            r'My Lord, ([^.]+\.)',
            r'Much obliged, My Lord\. ([^\\n]+)'
        ]

        for pattern in advocate_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                # Clean up the argument
                argument = re.sub(r'\s+', ' ', match.strip())
                if len(argument) > 20:  # Only meaningful arguments
                    arguments.append(argument)

        # Extract specific legal contentions
        contention_patterns = [
            r'contends that ([^.]+\.)',
            r'argues that ([^.]+\.)',
            r'submits that ([^.]+\.)',
            r'pleads that ([^.]+\.)'
        ]

        for pattern in contention_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                argument = match.strip()
                if len(argument) > 20:
                    arguments.append(f"Contention: {argument}")

        return arguments[:5] if arguments else ["Legal arguments not recorded"]

    def _extract_court_observations_enhanced(self, text: str) -> List[str]:
        """Enhanced court observations extraction"""
        observations = []

        # Extract court questions and observations
        court_patterns = [
            r'COURT: ([^\\n]+)',
            r'Judge: ([^\\n]+)',
            r'Your Lordship.*?: ([^\\n]+)'
        ]

        for pattern in court_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                observation = match.strip()
                if len(observation) > 10 and not observation.startswith('This matter'):
                    observations.append(observation)

        return observations[:5] if observations else ["Court observations not recorded"]

    def _extract_evidence_presented(self, text: str) -> List[str]:
        """Extract evidence presented in court"""
        evidence = []

        evidence_patterns = [
            r'evidence of ([^.]+\.)',
            r'recovered ([^.]+\.)',
            r'medical report ([^.]+\.)',
            r'forensic ([^.]+\.)',
            r'ballistic ([^.]+\.)',
            r'DNA ([^.]+\.)',
            r'fingerprint ([^.]+\.)'
        ]

        for pattern in evidence_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                evidence_item = match.strip()
                if len(evidence_item) > 10:
                    evidence.append(evidence_item)

        return evidence[:5] if evidence else ["Evidence details not specified"]

    def _extract_key_facts_enhanced(self, text: str) -> List[str]:
        """Extract key facts from the case"""
        facts = []

        fact_patterns = [
            r'fact that ([^.]+\.)',
            r'it is alleged that ([^.]+\.)',
            r'prosecution case is that ([^.]+\.)',
            r'incident occurred ([^.]+\.)',
            r'victim was ([^.]+\.)'
        ]

        for pattern in fact_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                fact = match.strip()
                if len(fact) > 15:
                    facts.append(fact)

        return facts[:5] if facts else ["Key facts not identified"]

    def _extract_next_date_enhanced(self, text: str) -> str:
        """Enhanced next hearing date extraction"""
        patterns = [
            r'adjourned to ([^\\n]+) for',
            r'next date.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})',
            r'case is adjourned to ([^\\n]+)',
            r'reconvene.*?(\d{1,2}[a-z]{2}\s+[A-Za-z]+,?\s+\d{4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                next_date = match.group(1).strip()
                return next_date

        return "Next date not specified"

    def _extract_judgment_status_enhanced(self, text: str) -> str:
        """Enhanced judgment status extraction"""
        status_indicators = [
            ('judgment is reserved', 'Judgment Reserved'),
            ('judgment pronounced', 'Judgment Pronounced'),
            ('arguments concluded', 'Arguments Concluded'),
            ('matter is heard', 'Arguments Heard'),
            ('order reserved', 'Order Reserved'),
            ('case is disposed', 'Case Disposed'),
            ('appeal is allowed', 'Appeal Allowed'),
            ('appeal is dismissed', 'Appeal Dismissed')
        ]

        text_lower = text.lower()
        for indicator, status in status_indicators:
            if indicator in text_lower:
                return status

        return "Proceedings ongoing"

# -----------------------------------------------------------------------------
# Enhanced Summary Generator
# -----------------------------------------------------------------------------
class EnhancedIndianSummaryGenerator:
    """Enhanced summary generator with improved narrative flow and meaningful responses"""

    def __init__(self, case_type: str):
        self.case_type = case_type

    def generate_enhanced_hybrid_summary(self, case_info: Dict, original_text: str) -> str:
        """Generate enhanced Indian legal hybrid summary"""

        # Generate enhanced narrative summary
        narrative_summary = self._generate_enhanced_narrative(case_info, original_text)

        # Generate enhanced structured analysis
        structured_analysis = self._generate_enhanced_structured_analysis(case_info)

        # Combine in enhanced Indian legal format
        hybrid_summary = f"""{'='*100}
ENHANCED INDIAN LEGAL CASE ANALYSIS - HYBRID FORMAT
{'='*100}

EXECUTIVE SUMMARY (कार्यकारी सारांश):
{'-'*60}
{narrative_summary}

{'='*100}
DETAILED STRUCTURED ANALYSIS (विस्तृत संरचित विश्लेषण)
{'='*100}
{structured_analysis}

{'='*100}
END OF ANALYSIS (विश्लेषण समाप्त)
{'='*100}"""

        return hybrid_summary

    def _generate_enhanced_narrative(self, case_info: Dict, original_text: str) -> str:
        """Generate enhanced narrative summary with better flow"""

        narrative_parts = []

        # 1. Case introduction with complete information
        intro = self._create_enhanced_case_introduction(case_info)
        if intro:
            narrative_parts.append(intro)

        # 2. Court and personnel information
        court_info = self._create_enhanced_court_info(case_info)
        if court_info:
            narrative_parts.append(court_info)

        # 3. Case facts and allegations
        facts = self._create_enhanced_facts_narrative(case_info, original_text)
        if facts:
            narrative_parts.append(facts)

        # 4. Legal proceedings and arguments
        proceedings = self._create_enhanced_proceedings_narrative(case_info, original_text)
        if proceedings:
            narrative_parts.append(proceedings)

        # 5. Outcome and next steps
        outcome = self._create_enhanced_outcome_narrative(case_info)
        if outcome:
            narrative_parts.append(outcome)

        return " ".join(narrative_parts)

    def _create_enhanced_case_introduction(self, case_info: Dict) -> str:
        """Create enhanced case introduction with complete details"""

        intro_parts = []

        # Case identification
        case_name = case_info.get('case_name', '')
        case_number = case_info.get('case_number', '')

        if case_name and 'not identified' not in case_name:
            intro = f"In the matter of {case_name}"
            if case_number and 'not identified' not in case_number:
                intro += f" ({case_number})"
            intro_parts.append(intro)

        # Court and judge details
        court_name = case_info.get('court_name', '')
        judge_name = case_info.get('judge_name', '')
        hearing_date = case_info.get('hearing_date', '')

        court_details = []
        if court_name and 'not identified' not in court_name:
            court_details.append(f"before the {court_name}")

        if judge_name and 'not identified' not in judge_name:
            court_details.append(f"presided over by Hon'ble {judge_name}")

        if hearing_date and 'not specified' not in hearing_date:
            court_details.append(f"on {hearing_date}")

        if court_details:
            intro_parts.append(", ".join(court_details))

        return ", ".join(intro_parts) + "." if intro_parts else ""

    def _create_enhanced_court_info(self, case_info: Dict) -> str:
        """Create enhanced court personnel information"""

        court_parts = []

        # Public Prosecutor
        pp = case_info.get('public_prosecutor', '')
        if pp and 'not identified' not in pp:
            court_parts.append(f"The case was argued by {pp} representing the State")

        # Defense advocates
        defense_advocates = case_info.get('defense_advocates', [])
        if defense_advocates and 'not identified' not in str(defense_advocates):
            if len(defense_advocates) == 1:
                court_parts.append(f"while the accused was represented by {defense_advocates[0]}")
            else:
                advocates_str = ', '.join(defense_advocates)
                court_parts.append(f"while the accused persons were represented by {advocates_str}")

        return ". ".join(court_parts) + "." if court_parts else ""

    def _create_enhanced_facts_narrative(self, case_info: Dict, original_text: str) -> str:
        """Create enhanced facts narrative"""

        facts_parts = []

        if self.case_type == 'criminal':
            facts_parts.extend(self._create_criminal_facts_enhanced(case_info, original_text))
        elif self.case_type == 'civil':
            facts_parts.extend(self._create_civil_facts_enhanced(case_info, original_text))
        elif self.case_type == 'family':
            facts_parts.extend(self._create_family_facts_enhanced(case_info, original_text))

        return ". ".join(facts_parts) + "." if facts_parts else ""

    def _create_criminal_facts_enhanced(self, case_info: Dict, original_text: str) -> List[str]:
        """Create enhanced criminal case facts"""

        facts = []

        # FIR and charges
        fir_details = case_info.get('fir_details', {})
        if fir_details.get('fir_number'):
            facts.append(f"An FIR No. {fir_details['fir_number']} was registered")

            if fir_details.get('sections'):
                facts.append(f"under Sections {fir_details['sections']}")

        # Police station and investigation
        police_station = case_info.get('police_station', '')
        if police_station and 'not identified' not in police_station:
            facts.append(f"Investigation was conducted by {police_station} Police Station")

        # Key facts from extraction
        key_facts = case_info.get('key_facts', [])
        if key_facts and 'not identified' not in str(key_facts):
            facts.extend(key_facts[:2])  # Add top 2 key facts

        # Witnesses
        pw_witnesses = case_info.get('prosecution_witnesses', [])
        if pw_witnesses and 'not identified' not in str(pw_witnesses):
            facts.append(f"The prosecution examined witnesses including {', '.join(pw_witnesses[:2])}")

        return facts

    def _create_civil_facts_enhanced(self, case_info: Dict, original_text: str) -> List[str]:
        """Create enhanced civil case facts"""

        facts = []

        if 'plaintiff' in original_text.lower():
            facts.append("The plaintiff filed a civil suit")

        if 'damages' in original_text.lower():
            facts.append("seeking damages and relief")

        if 'contract' in original_text.lower() or 'agreement' in original_text.lower():
            facts.append("arising from contractual disputes")

        return facts

    def _create_family_facts_enhanced(self, case_info: Dict, original_text: str) -> List[str]:
        """Create enhanced family case facts"""

        facts = []

        if 'custody' in original_text.lower():
            facts.append("The case involves child custody matters")

        if 'maintenance' in original_text.lower():
            facts.append("with issues of maintenance and support")

        if 'divorce' in original_text.lower():
            facts.append("in matrimonial proceedings")

        return facts

    def _create_enhanced_proceedings_narrative(self, case_info: Dict, original_text: str) -> str:
        """Create enhanced proceedings narrative"""

        proceedings_parts = []

        # Legal arguments
        legal_arguments = case_info.get('legal_arguments', [])
        if legal_arguments and 'not recorded' not in str(legal_arguments):
            proceedings_parts.append(f"The legal arguments centered on {legal_arguments[0]}")

        # Evidence presented
        evidence = case_info.get('evidence_presented', [])
        if evidence and 'not specified' not in str(evidence):
            proceedings_parts.append(f"Evidence presented included {evidence[0]}")

        # Case law cited
        case_law = case_info.get('case_law_cited', [])
        if case_law and 'No case law' not in str(case_law):
            proceedings_parts.append(f"The court referred to precedents including {case_law[0]}")

        return ". ".join(proceedings_parts) + "." if proceedings_parts else ""

    def _create_enhanced_outcome_narrative(self, case_info: Dict) -> str:
        """Create enhanced outcome narrative"""

        outcome_parts = []

        # Judgment status
        judgment_status = case_info.get('judgment_status', '')
        if judgment_status and 'ongoing' not in judgment_status:
            outcome_parts.append(f"The proceedings concluded with {judgment_status.lower()}")

        # Next date
        next_date = case_info.get('next_date', '')
        if next_date and 'not specified' not in next_date:
            outcome_parts.append(f"and the matter was adjourned to {next_date}")

        return ". ".join(outcome_parts) + "." if outcome_parts else "The matter remains pending before the court."

    def _generate_enhanced_structured_analysis(self, case_info: Dict) -> str:
        """Generate enhanced structured analysis with meaningful responses"""

        structure = f"""CASE IDENTIFICATION (मामले की पहचान):
Case Name: {case_info.get('case_name', 'Not identified')}
Case Number: {case_info.get('case_number', 'Not identified')}
Case Type: {case_info.get('case_type', 'Not specified').title()}
Court: {case_info.get('court_name', 'Not identified')}
Judge: {case_info.get('judge_name', 'Not identified')}
Hearing Date: {case_info.get('hearing_date', 'Not specified')}

LEGAL PERSONNEL (कानूनी कर्मचारी):
Public Prosecutor: {case_info.get('public_prosecutor', 'Not identified')}
Defense Advocates:
{self._format_list_enhanced(case_info.get('defense_advocates', ['Not identified']))}

PARTIES (पक्षकार):
Accused Persons:
{self._format_list_enhanced(case_info.get('accused_persons', ['Not identified']))}
Complainant: {case_info.get('complainant', 'Not identified')}
Victim: {case_info.get('victim', 'Not identified')}

FIR DETAILS (एफआईआर विवरण):
{self._format_fir_details(case_info.get('fir_details', {}))}
Police Station: {case_info.get('police_station', 'Not identified')}

LEGAL PROVISIONS (कानूनी प्रावधान):
IPC Sections:
{self._format_list_enhanced(case_info.get('ipc_sections', ['Not specified']))}
CrPC Sections:
{self._format_list_enhanced(case_info.get('crpc_sections', ['Not specified']))}
Evidence Act Sections:
{self._format_list_enhanced(case_info.get('evidence_act_sections', ['Not specified']))}

WITNESSES (गवाह):
Prosecution Witnesses:
{self._format_list_enhanced(case_info.get('prosecution_witnesses', ['Not identified']))}
Defense Witnesses:
{self._format_list_enhanced(case_info.get('defense_witnesses', ['Not identified']))}

EVIDENCE PRESENTED (प्रस्तुत साक्ष्य):
{self._format_list_enhanced(case_info.get('evidence_presented', ['Evidence details not specified']))}

KEY FACTS (मुख्य तथ्य):
{self._format_list_enhanced(case_info.get('key_facts', ['Key facts not identified']))}

CASE LAW CITED (उद्धृत मामला कानून):
{self._format_list_enhanced(case_info.get('case_law_cited', ['No case law cited']))}

LEGAL ARGUMENTS (कानूनी तर्क):
{self._format_list_enhanced(case_info.get('legal_arguments', ['Legal arguments not recorded']))}

COURT OBSERVATIONS (न्यायालय की टिप्पणियां):
{self._format_list_enhanced(case_info.get('court_observations', ['Court observations not recorded']))}

JUDGMENT STATUS (निर्णय की स्थिति):
Status: {case_info.get('judgment_status', 'Proceedings ongoing')}
Next Date: {case_info.get('next_date', 'Not specified')}"""

        return structure

    def _format_list_enhanced(self, items: List[str]) -> str:
        """Enhanced list formatting with better presentation"""
        if not items:
            return "• Not specified"

        # Filter out generic "not" responses
        meaningful_items = []
        for item in items:
            if item and not any(phrase in item.lower() for phrase in ['not specified', 'not identified', 'not recorded']):
                meaningful_items.append(item)

        if not meaningful_items:
            return "• Not specified"

        return '\n'.join([f"• {item}" for item in meaningful_items])

    def _format_fir_details(self, fir_details: Dict) -> str:
        """Format FIR details properly"""
        if not fir_details:
            return "• FIR details not available"

        details = []
        if fir_details.get('fir_number'):
            details.append(f"• FIR Number: {fir_details['fir_number']}")
        if fir_details.get('sections'):
            details.append(f"• Sections: {fir_details['sections']}")

        return '\n'.join(details) if details else "• FIR details not available"

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------
def main():
    logger.info("Starting Enhanced Indian Legal Summarizer...")

    # Read input
    try:
        with open(args.input, "r", encoding="utf-8") as f:
            text = f.read()
    except Exception as e:
        logger.error(f"Could not read input file: {e}")
        return

    logger.info(f"Input text length: {len(text)} characters")

    # Detect Indian case type
    case_type, confidence = detect_indian_case_type(text)
    logger.info(f"Detected Indian case type: {case_type} (confidence: {confidence:.2f})")

    # Extract enhanced Indian case information
    extractor = EnhancedIndianLegalExtractor(case_type)
    case_info = extractor.extract_enhanced_case_info(text)

    # Generate enhanced Indian hybrid summary
    generator = EnhancedIndianSummaryGenerator(case_type)
    enhanced_summary = generator.generate_enhanced_hybrid_summary(case_info, text)

    # Write output
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(enhanced_summary)
    except Exception as e:
        logger.error(f"Could not write output file: {e}")
        return

    logger.info(f"Enhanced Indian legal summary written to {args.output}")
    logger.info(f"Summary length: {len(enhanced_summary)} characters")

    print(enhanced_summary)

if __name__ == "__main__":
    main()