#!/usr/bin/env python3
"""
summarize_court_bart.py

Improved court proceedings summarization using BART model,
which is much better suited for summarization tasks.

Usage:
  python summarize_court_bart.py --input court_proceedings.txt --output court_summary.txt
"""

import argparse, logging, sys

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Court summarizer with BART")
parser.add_argument("-i","--input",  required=True, help="Raw transcript (.txt)")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-input",  type=int, default=1024, help="Max input tokens")
parser.add_argument("--max-output", type=int, default=256,   help="Max summary tokens")
parser.add_argument("--min-output", type=int, default=50,    help="Min summary tokens")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Check NumPy availability
# -----------------------------------------------------------------------------
try:
    import numpy as np
    _ = np.__version__
except ImportError:
    logger.error("NumPy is not installed. Please install it: pip install numpy")
    sys.exit(1)
except Exception as e:
    logger.error("NumPy load failed (%s).", e)
    sys.exit(1)

# -----------------------------------------------------------------------------
# 4) Import transformers/torch with error handling
# -----------------------------------------------------------------------------
try:
    from transformers import AutoTokenizer, AutoModelForSeq2SeqLM, pipeline
    import torch
except ImportError as e:
    logger.error("Missing Python package: %s", e)
    logger.error("Please install dependencies:\n  pip install transformers torch")
    sys.exit(1)
except Exception as e:
    logger.error("Error importing transformers/torch: %s", e)
    sys.exit(1)

device = "cpu"  # CPU-only for compatibility

# -----------------------------------------------------------------------------
# 5) Read & clean text
# -----------------------------------------------------------------------------
def read_and_clean(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            raw = f.read()
    except Exception as e:
        logger.error("Could not read input file: %s", e)
        sys.exit(1)
    # collapse blank lines and clean up
    lines = [ln.strip() for ln in raw.splitlines() if ln.strip()]
    return "\n".join(lines)

# -----------------------------------------------------------------------------
# 6) Chunk text for long documents
# -----------------------------------------------------------------------------
def chunk_text(text: str, max_chunk_size: int = 800) -> list:
    """Split text into chunks that fit within model limits"""
    words = text.split()
    chunks = []
    current_chunk = []
    current_size = 0
    
    for word in words:
        if current_size + len(word) + 1 > max_chunk_size and current_chunk:
            chunks.append(" ".join(current_chunk))
            current_chunk = [word]
            current_size = len(word)
        else:
            current_chunk.append(word)
            current_size += len(word) + 1
    
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    
    return chunks

# -----------------------------------------------------------------------------
# 7) Summarize using BART
# -----------------------------------------------------------------------------
def summarize_with_bart(text: str):
    try:
        logger.info("Loading BART model for summarization...")
        
        # Use the summarization pipeline with BART
        summarizer = pipeline(
            "summarization",
            model="facebook/bart-large-cnn",
            device=0 if torch.cuda.is_available() else -1,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        # For long documents, we need to chunk them
        chunks = chunk_text(text, max_chunk_size=800)
        logger.info(f"Processing {len(chunks)} chunks...")
        
        chunk_summaries = []
        for i, chunk in enumerate(chunks):
            logger.info(f"Summarizing chunk {i+1}/{len(chunks)}...")
            
            # Add legal context to the chunk
            legal_prompt = f"Court proceedings summary: {chunk}"
            
            summary = summarizer(
                legal_prompt,
                max_length=args.max_output // len(chunks) + 50,  # Distribute output length
                min_length=args.min_output // len(chunks),
                do_sample=False,
                num_beams=4,
                length_penalty=2.0,
                early_stopping=True
            )
            
            chunk_summaries.append(summary[0]['summary_text'])
        
        # If we have multiple chunks, summarize the summaries
        if len(chunk_summaries) > 1:
            logger.info("Creating final summary from chunk summaries...")
            combined_summaries = " ".join(chunk_summaries)
            
            final_summary = summarizer(
                f"Legal case summary: {combined_summaries}",
                max_length=args.max_output,
                min_length=args.min_output,
                do_sample=False,
                num_beams=4,
                length_penalty=2.0,
                early_stopping=True
            )
            return final_summary[0]['summary_text']
        else:
            return chunk_summaries[0]
            
    except Exception as e:
        logger.error("Summarization failed: %s", e)
        sys.exit(1)

# -----------------------------------------------------------------------------
# 8) Main
# -----------------------------------------------------------------------------
def main():
    text = read_and_clean(args.input)
    logger.info(f"Input text length: {len(text)} characters")
    
    summary = summarize_with_bart(text)
    
    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error("Could not write output file: %s", e)
        sys.exit(1)

    logger.info("Summary written to %s", args.output)
    logger.info(f"Summary length: {len(summary)} characters")
    print("\n" + "="*50)
    print("GENERATED SUMMARY:")
    print("="*50)
    print(summary)
    print("="*50)

if __name__ == "__main__":
    main()
