#!/usr/bin/env python3
"""
summarize_court.py

Abstractive summarization of courtroom proceedings using LongT5,
with robust error handling for NumPy/torch/transformers issues.

Usage:
  python summarize_court.py --input court_proceedings.txt --output court_summary.txt
"""

import argparse, logging, sys

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Court summarizer with LongT5")
parser.add_argument("-i","--input",  required=True, help="Raw transcript (.txt)")
parser.add_argument("-o","--output", required=True, help="Output summary file")
parser.add_argument("--max-input",  type=int, default=16000, help="Max input tokens")
parser.add_argument("--max-output", type=int, default=512,   help="Max summary tokens")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Check NumPy C-API availability
# -----------------------------------------------------------------------------
try:
    import numpy as np
    _ = np.__version__
except ImportError:
    logger.error("NumPy is not installed. Please install it: conda install -c conda-forge numpy")
    sys.exit(1)
except Exception as e:
    logger.error("NumPy C‐extension load failed (%s).", e)
    logger.error("If you’re in conda, run:\n  conda remove --force numpy scipy scikit-learn\n  conda install -c conda-forge numpy=1.26.4 scipy scikit-learn")
    sys.exit(1)

# -----------------------------------------------------------------------------
# 4) Import transformers/torch with error handling
# -----------------------------------------------------------------------------
try:
    from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
    import torch
except ImportError as e:
    logger.error("Missing Python package: %s", e)
    logger.error("Please install dependencies:\n  python -m pip install --upgrade pip setuptools wheel\n  python -m pip install transformers torch sentencepiece")
    sys.exit(1)
except Exception as e:
    logger.error("Error importing transformers/torch: %s", e)
    sys.exit(1)

device = "cpu"  # CPU-only

# -----------------------------------------------------------------------------
# 5) Load model & tokenizer
# -----------------------------------------------------------------------------
def load_model(name: str, device: str):
    try:
        logger.info("Loading model and tokenizer: %s", name)
        tok = AutoTokenizer.from_pretrained(name)
        mdl = AutoModelForSeq2SeqLM.from_pretrained(name).to(device)
        return tok, mdl
    except Exception as e:
        logger.error("Failed to load model/tokenizer: %s", e)
        logger.error("Check your internet connection and library versions.")
        sys.exit(1)

# -----------------------------------------------------------------------------
# 6) Read & clean text
# -----------------------------------------------------------------------------
def read_and_clean(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            raw = f.read()
    except Exception as e:
        logger.error("Could not read input file: %s", e)
        sys.exit(1)
    # collapse blank lines
    return "\n".join([ln.strip() for ln in raw.splitlines() if ln.strip()])

# -----------------------------------------------------------------------------
# 7) Summarize
# -----------------------------------------------------------------------------
def summarize(text: str, tokenizer, model):
    try:
        # T5 models expect task prefixes - use "summarize:" for summarization
        prompt = f"summarize: {text}"

        inputs = tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=args.max_input,
            padding=True
        )
        input_ids      = inputs["input_ids"].to(device)
        attention_mask = inputs["attention_mask"].to(device)

        logger.info("Generating summary…")
        logger.info("Input length: %d tokens", input_ids.shape[1])

        # Generate summary with better parameters for T5
        with torch.no_grad():
            summary_ids = model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                max_length=args.max_output,
                min_length=30,
                num_beams=4,
                length_penalty=2.0,
                early_stopping=True,
                no_repeat_ngram_size=3
            )

        # Decode the generated summary
        summary = tokenizer.decode(summary_ids[0], skip_special_tokens=True)

        logger.info("Generated summary length: %d characters", len(summary))
        return summary.strip()
    except Exception as e:
        logger.error("Summarization failed: %s", e)
        sys.exit(1)

# -----------------------------------------------------------------------------
# 8) Main
# -----------------------------------------------------------------------------
def main():
    tokenizer, model = load_model("google/long-t5-tglobal-base", device)
    text = read_and_clean(args.input)
    summary = summarize(text, tokenizer, model)

    try:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(summary)
    except Exception as e:
        logger.error("Could not write output file: %s", e)
        sys.exit(1)

    logger.info("Summary written to %s", args.output)


if __name__ == "__main__":
    main()
