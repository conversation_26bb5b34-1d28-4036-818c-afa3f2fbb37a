#!/usr/bin/env python3
"""
fine_tune_legal_bart.py

<PERSON>ript to fine-tune BART on legal documents for better court proceeding summarization.
This creates a custom legal BART model specifically for your use case.

Usage:
  1. Prepare your dataset: court_proceedings.jsonl with {"text": "...", "summary": "..."}
  2. Run: python fine_tune_legal_bart.py --dataset court_proceedings.jsonl --output legal-bart-model
"""

import argparse, logging, json, sys
from pathlib import Path

# -----------------------------------------------------------------------------
# 1) Arg parsing
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser("Fine-tune BART for legal document summarization")
parser.add_argument("--dataset", required=True, help="JSONL file with text/summary pairs")
parser.add_argument("--output", required=True, help="Output directory for fine-tuned model")
parser.add_argument("--epochs", type=int, default=3, help="Number of training epochs")
parser.add_argument("--batch-size", type=int, default=4, help="Training batch size")
parser.add_argument("--max-input", type=int, default=1024, help="Max input length")
parser.add_argument("--max-output", type=int, default=256, help="Max output length")
args = parser.parse_args()

# -----------------------------------------------------------------------------
# 2) Logging
# -----------------------------------------------------------------------------
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 3) Import dependencies
# -----------------------------------------------------------------------------
try:
    from transformers import (
        AutoTokenizer, AutoModelForSeq2SeqLM, 
        TrainingArguments, Trainer, DataCollatorForSeq2Seq
    )
    from datasets import Dataset
    import torch
    import numpy as np
except ImportError as e:
    logger.error("Missing packages: %s", e)
    logger.error("Install with: pip install transformers datasets torch")
    sys.exit(1)

# -----------------------------------------------------------------------------
# 4) Load and prepare dataset
# -----------------------------------------------------------------------------
def load_legal_dataset(file_path: str):
    """Load legal document dataset from JSONL file"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                item = json.loads(line.strip())
                if 'text' in item and 'summary' in item:
                    data.append({
                        'text': item['text'],
                        'summary': item['summary']
                    })
    except Exception as e:
        logger.error(f"Error loading dataset: {e}")
        sys.exit(1)
    
    logger.info(f"Loaded {len(data)} legal document pairs")
    return Dataset.from_list(data)

def preprocess_function(examples, tokenizer):
    """Preprocess the dataset for BART fine-tuning"""
    # Add legal-specific prefix
    inputs = [f"Legal case summary: {text}" for text in examples["text"]]
    targets = examples["summary"]
    
    # Tokenize inputs
    model_inputs = tokenizer(
        inputs, 
        max_length=args.max_input, 
        truncation=True, 
        padding=True
    )
    
    # Tokenize targets
    with tokenizer.as_target_tokenizer():
        labels = tokenizer(
            targets, 
            max_length=args.max_output, 
            truncation=True, 
            padding=True
        )
    
    model_inputs["labels"] = labels["input_ids"]
    return model_inputs

# -----------------------------------------------------------------------------
# 5) Fine-tuning setup
# -----------------------------------------------------------------------------
def fine_tune_legal_bart():
    logger.info("Starting BART fine-tuning for legal documents...")
    
    # Load base model and tokenizer
    model_name = "facebook/bart-large-cnn"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
    
    # Load dataset
    dataset = load_legal_dataset(args.dataset)
    
    # Split dataset
    train_size = int(0.8 * len(dataset))
    train_dataset = dataset.select(range(train_size))
    eval_dataset = dataset.select(range(train_size, len(dataset)))
    
    # Preprocess datasets
    train_dataset = train_dataset.map(
        lambda x: preprocess_function(x, tokenizer),
        batched=True,
        remove_columns=dataset.column_names
    )
    
    eval_dataset = eval_dataset.map(
        lambda x: preprocess_function(x, tokenizer),
        batched=True,
        remove_columns=dataset.column_names
    )
    
    # Data collator
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=model,
        padding=True
    )
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir=args.output,
        num_train_epochs=args.epochs,
        per_device_train_batch_size=args.batch_size,
        per_device_eval_batch_size=args.batch_size,
        warmup_steps=500,
        weight_decay=0.01,
        logging_dir=f"{args.output}/logs",
        logging_steps=100,
        evaluation_strategy="steps",
        eval_steps=500,
        save_steps=1000,
        save_total_limit=2,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to=None,  # Disable wandb
        push_to_hub=False
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator
    )
    
    # Fine-tune the model
    logger.info("Starting training...")
    trainer.train()
    
    # Save the final model
    logger.info(f"Saving fine-tuned model to {args.output}")
    trainer.save_model()
    tokenizer.save_pretrained(args.output)
    
    logger.info("Fine-tuning completed!")

# -----------------------------------------------------------------------------
# 6) Create sample dataset
# -----------------------------------------------------------------------------
def create_sample_dataset():
    """Create a sample dataset for demonstration"""
    sample_data = [
        {
            "text": "JUDGE: Good morning. We are here for State v. Anderson. PROSECUTOR: The State calls Officer Cheng. CHENG: I found the victim with a gunshot wound. A revolver was under the sofa. PROSECUTOR: The State calls Dr. Gupta. GUPTA: The victim died from a gunshot wound. The angle indicates the shooter was standing over the victim. DEFENSE: Mr. Anderson, did you fire the gun? ANDERSON: No, she grabbed it and it went off.",
            "summary": "In State v. Anderson, Officer Cheng testified about finding the victim with a gunshot wound and a revolver under the sofa. Dr. Gupta's autopsy revealed the shooter was standing over the victim. Anderson claimed the gun went off when the victim grabbed it."
        },
        {
            "text": "JUDGE: This is a hearing for Smith v. Jones. PROSECUTOR: The defendant was seen at the scene. WITNESS: I saw the defendant running from the building at 10 PM. DEFENSE: My client was elsewhere. DEFENDANT: I was at home watching TV.",
            "summary": "In Smith v. Jones, a witness testified seeing the defendant running from the building at 10 PM, while the defense claimed the defendant was at home."
        }
    ]
    
    with open("sample_legal_dataset.jsonl", "w", encoding="utf-8") as f:
        for item in sample_data:
            f.write(json.dumps(item) + "\n")
    
    logger.info("Created sample_legal_dataset.jsonl for demonstration")

# -----------------------------------------------------------------------------
# 7) Main
# -----------------------------------------------------------------------------
def main():
    if not Path(args.dataset).exists():
        logger.info("Dataset not found. Creating sample dataset...")
        create_sample_dataset()
        logger.info("Please prepare your actual dataset and run again.")
        logger.info("Dataset format: JSONL with {'text': '...', 'summary': '...'}")
        return
    
    fine_tune_legal_bart()

if __name__ == "__main__":
    main()
